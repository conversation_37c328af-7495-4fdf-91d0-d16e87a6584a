import fs from 'fs';
import { Readable, Transform } from 'stream';
import { pipeline } from 'stream/promises';
import logger from '../utils/logger';
import config from '../config';

export interface MemoryUsage {
  rss: number; // Resident Set Size
  heapTotal: number;
  heapUsed: number;
  external: number;
  arrayBuffers: number;
}

export interface MemoryThresholds {
  warning: number; // MB
  critical: number; // MB
  maxFileSize: number; // MB
}

export class MemoryManager {
  private static thresholds: MemoryThresholds = {
    warning: 512, // 512MB
    critical: 1024, // 1GB
    maxFileSize: 100 // 100MB for streaming
  };

  /**
   * Get current memory usage in MB
   */
  static getMemoryUsage(): MemoryUsage & { formatted: Record<string, string> } {
    const usage = process.memoryUsage();
    
    return {
      ...usage,
      formatted: {
        rss: this.formatBytes(usage.rss),
        heapTotal: this.formatBytes(usage.heapTotal),
        heapUsed: this.formatBytes(usage.heapUsed),
        external: this.formatBytes(usage.external),
        arrayBuffers: this.formatBytes(usage.arrayBuffers)
      }
    };
  }

  /**
   * Check if memory usage is within safe limits
   */
  static checkMemoryLimits(): { 
    status: 'safe' | 'warning' | 'critical';
    usage: MemoryUsage;
    message: string;
  } {
    const usage = this.getMemoryUsage();
    const heapUsedMB = usage.heapUsed / (1024 * 1024);
    
    if (heapUsedMB > this.thresholds.critical) {
      return {
        status: 'critical',
        usage,
        message: `Critical memory usage: ${usage.formatted.heapUsed}`
      };
    } else if (heapUsedMB > this.thresholds.warning) {
      return {
        status: 'warning',
        usage,
        message: `High memory usage: ${usage.formatted.heapUsed}`
      };
    }
    
    return {
      status: 'safe',
      usage,
      message: `Memory usage normal: ${usage.formatted.heapUsed}`
    };
  }

  /**
   * Force garbage collection if available
   */
  static forceGarbageCollection(): void {
    if (global.gc) {
      logger.info('Forcing garbage collection');
      global.gc();
    } else {
      logger.warn('Garbage collection not available. Start Node.js with --expose-gc flag');
    }
  }

  /**
   * Check if file should be processed with streaming
   */
  static shouldUseStreaming(fileSize: number): boolean {
    const fileSizeMB = fileSize / (1024 * 1024);
    return fileSizeMB > this.thresholds.maxFileSize;
  }

  /**
   * Create a memory-safe file reader stream
   */
  static createFileStream(filePath: string, options?: {
    highWaterMark?: number;
    start?: number;
    end?: number;
  }): Readable {
    const defaultOptions = {
      highWaterMark: 64 * 1024, // 64KB chunks
      ...options
    };
    
    return fs.createReadStream(filePath, defaultOptions);
  }

  /**
   * Create a transform stream that monitors memory usage
   */
  static createMemoryMonitorStream(): Transform {
    let bytesProcessed = 0;
    let lastMemoryCheck = Date.now();
    
    return new Transform({
      transform(chunk, encoding, callback) {
        bytesProcessed += chunk.length;
        
        // Check memory every 10MB or 5 seconds
        const now = Date.now();
        if (bytesProcessed % (10 * 1024 * 1024) === 0 || now - lastMemoryCheck > 5000) {
          const memoryStatus = MemoryManager.checkMemoryLimits();
          
          if (memoryStatus.status === 'critical') {
            logger.error('Critical memory usage during streaming:', memoryStatus.message);
            MemoryManager.forceGarbageCollection();
            
            // If still critical after GC, abort
            const afterGC = MemoryManager.checkMemoryLimits();
            if (afterGC.status === 'critical') {
              return callback(new Error('Memory limit exceeded during file processing'));
            }
          } else if (memoryStatus.status === 'warning') {
            logger.warn('High memory usage during streaming:', memoryStatus.message);
            MemoryManager.forceGarbageCollection();
          }
          
          lastMemoryCheck = now;
        }
        
        callback(null, chunk);
      }
    });
  }

  /**
   * Process large file with streaming and memory monitoring
   */
  static async processLargeFile<T>(
    inputPath: string,
    outputPath: string,
    processor: (inputStream: Readable, outputStream: NodeJS.WritableStream) => Promise<T>
  ): Promise<T> {
    const inputStream = this.createFileStream(inputPath);
    const outputStream = fs.createWriteStream(outputPath);
    const memoryMonitor = this.createMemoryMonitorStream();
    
    try {
      logger.info(`Starting streaming processing for large file: ${inputPath}`);
      
      // Monitor memory before starting
      const initialMemory = this.checkMemoryLimits();
      logger.info(`Initial memory status: ${initialMemory.message}`);
      
      const result = await processor(
        inputStream.pipe(memoryMonitor),
        outputStream
      );
      
      // Final memory check
      const finalMemory = this.checkMemoryLimits();
      logger.info(`Final memory status: ${finalMemory.message}`);
      
      return result;
      
    } catch (error) {
      logger.error('Error during streaming file processing:', error);
      
      // Clean up output file on error
      if (fs.existsSync(outputPath)) {
        fs.unlinkSync(outputPath);
      }
      
      throw error;
    } finally {
      // Ensure streams are closed
      inputStream.destroy();
      outputStream.destroy();
      
      // Force garbage collection
      this.forceGarbageCollection();
    }
  }

  /**
   * Copy file with streaming and progress monitoring
   */
  static async copyFileWithProgress(
    sourcePath: string,
    destPath: string,
    onProgress?: (bytesProcessed: number, totalBytes: number) => void
  ): Promise<void> {
    const stats = fs.statSync(sourcePath);
    const totalBytes = stats.size;
    let bytesProcessed = 0;
    
    const progressTransform = new Transform({
      transform(chunk, encoding, callback) {
        bytesProcessed += chunk.length;
        
        if (onProgress) {
          onProgress(bytesProcessed, totalBytes);
        }
        
        callback(null, chunk);
      }
    });
    
    const inputStream = this.createFileStream(sourcePath);
    const outputStream = fs.createWriteStream(destPath);
    const memoryMonitor = this.createMemoryMonitorStream();
    
    await pipeline(
      inputStream,
      memoryMonitor,
      progressTransform,
      outputStream
    );
  }

  /**
   * Format bytes to human readable string
   */
  private static formatBytes(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  /**
   * Set custom memory thresholds
   */
  static setThresholds(thresholds: Partial<MemoryThresholds>): void {
    this.thresholds = { ...this.thresholds, ...thresholds };
    logger.info('Updated memory thresholds:', this.thresholds);
  }

  /**
   * Get current thresholds
   */
  static getThresholds(): MemoryThresholds {
    return { ...this.thresholds };
  }

  /**
   * Start memory monitoring interval
   */
  static startMemoryMonitoring(intervalMs: number = 30000): NodeJS.Timeout {
    return setInterval(() => {
      const status = this.checkMemoryLimits();
      
      if (status.status !== 'safe') {
        logger.warn(`Memory monitoring alert: ${status.message}`);
        
        if (status.status === 'critical') {
          this.forceGarbageCollection();
        }
      }
    }, intervalMs);
  }
}
