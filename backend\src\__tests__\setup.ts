import fs from 'fs';
import path from 'path';
import config from '../config';

// Setup test environment
beforeAll(() => {
  // Ensure test directories exist
  const testDirs = [
    path.join(__dirname, '../../test-uploads'),
    path.join(__dirname, '../../test-temp'),
  ];

  testDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
});

// Cleanup after tests
afterAll(() => {
  // Clean up test files
  const testDirs = [
    path.join(__dirname, '../../test-uploads'),
    path.join(__dirname, '../../test-temp'),
  ];

  testDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
    }
  });
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
