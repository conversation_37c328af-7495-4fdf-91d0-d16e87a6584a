{"name": "pdf-toolkit-backend", "version": "1.0.0", "description": "Backend API for PDF toolkit application", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "winston": "^3.11.0", "joi": "^17.11.0", "pdf-lib": "^1.17.1", "pdf2pic": "^3.1.1", "sharp": "^0.33.0", "mammoth": "^1.6.0", "xlsx": "^0.18.5", "uuid": "^9.0.1", "dotenv": "^16.3.1", "compression": "^1.7.4", "express-fileupload": "^1.4.3", "ghostscript4js": "^3.2.1", "node-libreoffice": "^0.2.1", "socket.io": "^4.7.4", "bull": "^4.12.2", "redis": "^4.6.12", "ioredis": "^5.3.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/compression": "^1.7.5", "@types/express-fileupload": "^1.4.4", "@types/node": "^20.10.0", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.2", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3"}, "keywords": ["pdf", "api", "express", "typescript", "file-processing"], "author": "PDF Toolkit", "license": "MIT"}