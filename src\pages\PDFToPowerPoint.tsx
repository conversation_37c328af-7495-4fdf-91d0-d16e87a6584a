import React, { useState } from 'react';
import { FileText, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import apiService from '../services/api';

const PDFToPowerPoint = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [outputFormat, setOutputFormat] = useState<'pptx' | 'ppt'>('pptx');
  const [downloadUrl, setDownloadUrl] = useState<string>('');
  const [error, setError] = useState<string>('');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    setDownloadUrl('');
    setError('');
  };

  const handleConvert = async () => {
    if (files.length === 0) return;

    setIsProcessing(true);
    setError('');

    try {
      const response = await apiService.pdfToPowerPoint(files[0]);

      if (response.success && response.data) {
        setDownloadUrl(response.data.downloadUrl || '');
        alert(response.message || 'PDF converti en PowerPoint avec succès!');
      } else {
        throw new Error(response.error || 'Erreur lors de la conversion');
      }
    } catch (error) {
      console.error('Conversion error:', error);
      setError(error instanceof Error ? error.message : 'Erreur lors de la conversion PDF vers PowerPoint');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownload = async () => {
    if (!downloadUrl) return;

    try {
      const fileId = downloadUrl.split('/').pop();
      if (!fileId) return;

      const blob = await apiService.downloadFile(fileId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `converted.${outputFormat}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download error:', error);
      setError('Erreur lors du téléchargement');
    }
  };

  return (
    <ToolLayout
      title="PDF en PowerPoint"
      description="Transformez vos fichiers PDF en présentations PPT et PPTX faciles à éditer"
      icon={<FileText className="w-8 h-8" />}
      color="from-orange-500 to-orange-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Format de sortie
            </h3>
            
            <div className="space-y-4">
              <label className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="format"
                  value="pptx"
                  checked={outputFormat === 'pptx'}
                  onChange={(e) => setOutputFormat(e.target.value as 'pptx')}
                  className="text-orange-600"
                />
                <div>
                  <span className="text-slate-700 font-medium">PPTX (recommandé)</span>
                  <p className="text-sm text-slate-500">Compatible avec PowerPoint 2007 et versions ultérieures</p>
                </div>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="format"
                  value="ppt"
                  checked={outputFormat === 'ppt'}
                  onChange={(e) => setOutputFormat(e.target.value as 'ppt')}
                  className="text-orange-600"
                />
                <div>
                  <span className="text-slate-700 font-medium">PPT</span>
                  <p className="text-sm text-slate-500">Compatible avec les anciennes versions de PowerPoint</p>
                </div>
              </label>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-orange-600 to-red-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <span>Convertir en PowerPoint</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default PDFToPowerPoint;