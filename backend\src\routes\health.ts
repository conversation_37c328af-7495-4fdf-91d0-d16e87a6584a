import { Router, Request, Response } from 'express';
import config from '../config';
import { FileUtils } from '../utils/fileUtils';

const router = Router();

router.get('/', (req: Request, res: Response) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: config.server.nodeEnv,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      limits: {
        maxFileSize: FileUtils.formatFileSize(config.upload.maxFileSize),
        maxTotalSize: FileUtils.formatFileSize(config.upload.maxTotalSize),
      }
    }
  });
});

export default router;
