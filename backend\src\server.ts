import express from 'express';
import compression from 'compression';
import cors from 'cors';
import config from './config';
import logger from './utils/logger';
import { FileUtils } from './utils/fileUtils';
import {
  rateLimiter,
  corsOptions,
  securityHeaders,
  requestLogger,
  errorHandler,
  notFoundHandler
} from './middleware/security';
import { handleUploadError } from './middleware/upload';

// Import routes (will be created in next steps)
import pdfRoutes from './routes/pdf';
import downloadRoutes from './routes/download';
import healthRoutes from './routes/health';

const app = express();

// Trust proxy for rate limiting and IP detection
app.set('trust proxy', 1);

// Security middleware
app.use(securityHeaders);
app.use(cors(corsOptions));
app.use(rateLimiter);

// Body parsing middleware
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging
app.use(requestLogger);

// Ensure required directories exist
FileUtils.ensureDirectoryExists(config.paths.uploads);
FileUtils.ensureDirectoryExists(config.paths.temp);
FileUtils.ensureDirectoryExists(config.paths.logs);

// Routes
app.use('/api/health', healthRoutes);
app.use('/api/pdf', pdfRoutes);
app.use('/api/download', downloadRoutes);

// Upload error handling
app.use(handleUploadError);

// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Start cleanup interval
setInterval(() => {
  FileUtils.cleanupOldFiles().catch(error => {
    logger.error('Error during file cleanup:', error);
  });
}, config.cleanup.intervalMs);

// Start server
const server = app.listen(config.server.port, () => {
  logger.info(`Server running on port ${config.server.port} in ${config.server.nodeEnv} mode`);
  logger.info(`CORS enabled for: ${config.cors.frontendUrl}`);
  logger.info(`File upload limit: ${FileUtils.formatFileSize(config.upload.maxFileSize)}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

export default app;
