import express from 'express';
import compression from 'compression';
import cors from 'cors';
import { createServer } from 'http';
import config from './config';
import logger from './utils/logger';
import { FileUtils } from './utils/fileUtils';
import { MemoryManager } from './services/MemoryManager';
import { WebSocketService } from './services/WebSocketService';
import {
  rateLimiter,
  corsOptions,
  securityHeaders,
  requestLogger,
  errorHandler,
  notFoundHandler
} from './middleware/security';
import { handleUploadError } from './middleware/upload';

// Import routes (will be created in next steps)
import pdfRoutes from './routes/pdf';
import downloadRoutes from './routes/download';
import healthRoutes from './routes/health';

const app = express();
const httpServer = createServer(app);

// Initialize WebSocket service
const webSocketService = new WebSocketService(httpServer);

// Trust proxy for rate limiting and IP detection
app.set('trust proxy', 1);

// Security middleware
app.use(securityHeaders);
app.use(cors(corsOptions));
app.use(rateLimiter);

// Body parsing middleware
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging
app.use(requestLogger);

// Ensure required directories exist
FileUtils.ensureDirectoryExists(config.paths.uploads);
FileUtils.ensureDirectoryExists(config.paths.temp);
FileUtils.ensureDirectoryExists(config.paths.logs);

// Routes
app.use('/api/health', healthRoutes);
app.use('/api/pdf', pdfRoutes);
app.use('/api/download', downloadRoutes);

// Upload error handling
app.use(handleUploadError);

// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Start cleanup interval
setInterval(() => {
  FileUtils.cleanupOldFiles().catch(error => {
    logger.error('Error during file cleanup:', error);
  });
}, config.cleanup.intervalMs);

// Start memory monitoring
const memoryMonitorInterval = MemoryManager.startMemoryMonitoring(30000); // Every 30 seconds
logger.info('Memory monitoring started');

// Start periodic WebSocket status broadcasts
const statusBroadcastInterval = webSocketService.startPeriodicStatusBroadcast(30000);
logger.info('WebSocket status broadcasting started');

// Start server
const server = httpServer.listen(config.server.port, () => {
  logger.info(`Server running on port ${config.server.port} in ${config.server.nodeEnv} mode`);
  logger.info(`CORS enabled for: ${config.cors.frontendUrl}`);
  logger.info(`File upload limit: ${FileUtils.formatFileSize(config.upload.maxFileSize)}`);
  logger.info('WebSocket server ready for connections');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');

  // Clear intervals
  clearInterval(memoryMonitorInterval);
  clearInterval(statusBroadcastInterval);

  // Disconnect WebSocket clients
  webSocketService.disconnectAllClients();

  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

// Export WebSocket service for use in other modules
export { webSocketService };

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

export default app;
