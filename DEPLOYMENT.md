# PDF Toolkit - Deployment Guide

This guide covers deploying the PDF Toolkit application to various hosting platforms.

## 🚀 Quick Deployment Options

### Option 1: Railway (Recommended for Backend)

Railway provides excellent Node.js hosting with automatic deployments.

#### Backend Deployment on Railway

1. **Create Railway Account**
   - Visit [railway.app](https://railway.app)
   - Sign up with GitHub

2. **Deploy Backend**
   ```bash
   # Install Railway CLI
   npm install -g @railway/cli
   
   # Login to Railway
   railway login
   
   # Navigate to backend directory
   cd backend
   
   # Initialize Railway project
   railway init
   
   # Deploy
   railway up
   ```

3. **Set Environment Variables**
   ```bash
   railway variables set NODE_ENV=production
   railway variables set PORT=3001
   railway variables set FRONTEND_URL=https://your-frontend-domain.com
   railway variables set MAX_FILE_SIZE=********
   railway variables set MAX_TOTAL_SIZE=*********
   ```

4. **Custom Domain (Optional)**
   - Go to Railway dashboard
   - Add custom domain in settings

### Option 2: Vercel (Recommended for Frontend)

Vercel provides excellent React hosting with automatic deployments.

#### Frontend Deployment on Vercel

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Deploy Frontend**
   ```bash
   # From project root
   vercel
   
   # Follow prompts to configure
   ```

3. **Set Environment Variables**
   - Go to Vercel dashboard
   - Project Settings → Environment Variables
   - Add: `VITE_API_URL=https://your-backend-domain.com/api`

4. **Build Settings**
   - Build Command: `npm run build`
   - Output Directory: `dist`
   - Install Command: `npm install`

### Option 3: DigitalOcean App Platform

Full-stack deployment on a single platform.

#### Deploy Both Frontend and Backend

1. **Create DigitalOcean Account**
   - Visit [digitalocean.com](https://digitalocean.com)

2. **Create App**
   - Go to Apps section
   - Create new app from GitHub repository

3. **Configure Services**
   
   **Backend Service:**
   - Source: `/backend`
   - Build Command: `npm run build`
   - Run Command: `npm start`
   - Environment Variables:
     ```
     NODE_ENV=production
     PORT=8080
     FRONTEND_URL=${frontend.RENDERED_URL}
     ```

   **Frontend Service:**
   - Source: `/`
   - Build Command: `npm run build`
   - Output Directory: `dist`
   - Environment Variables:
     ```
     VITE_API_URL=${backend.RENDERED_URL}/api
     ```

## 🐳 Docker Deployment

### Backend Dockerfile

Create `backend/Dockerfile`:

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Create directories for file uploads
RUN mkdir -p uploads temp logs
RUN chown -R nodejs:nodejs uploads temp logs

USER nodejs

EXPOSE 3001

CMD ["npm", "start"]
```

### Frontend Dockerfile

Create `Dockerfile`:

```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built files
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### Docker Compose

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - FRONTEND_URL=http://localhost:80
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/temp:/app/temp
      - ./backend/logs:/app/logs

  frontend:
    build: .
    ports:
      - "80:80"
    environment:
      - VITE_API_URL=http://localhost:3001/api
    depends_on:
      - backend
```

### Deploy with Docker

```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## ☁️ AWS Deployment

### Backend on AWS Lambda (Serverless)

1. **Install Serverless Framework**
   ```bash
   npm install -g serverless
   ```

2. **Create `serverless.yml`**
   ```yaml
   service: pdf-toolkit-backend
   
   provider:
     name: aws
     runtime: nodejs18.x
     region: us-east-1
     
   functions:
     api:
       handler: dist/lambda.handler
       events:
         - http:
             path: /{proxy+}
             method: ANY
             cors: true
   ```

3. **Create Lambda Handler**
   Create `backend/src/lambda.ts`:
   ```typescript
   import serverless from 'serverless-http';
   import app from './server';
   
   export const handler = serverless(app);
   ```

4. **Deploy**
   ```bash
   cd backend
   serverless deploy
   ```

### Frontend on AWS S3 + CloudFront

1. **Build Frontend**
   ```bash
   npm run build
   ```

2. **Create S3 Bucket**
   ```bash
   aws s3 mb s3://your-pdf-toolkit-frontend
   ```

3. **Upload Files**
   ```bash
   aws s3 sync dist/ s3://your-pdf-toolkit-frontend --delete
   ```

4. **Configure S3 for Static Hosting**
   ```bash
   aws s3 website s3://your-pdf-toolkit-frontend \
     --index-document index.html \
     --error-document index.html
   ```

## 🔧 Production Optimizations

### Backend Optimizations

1. **Enable Compression**
   - Already included in the backend setup

2. **Configure Logging**
   ```env
   LOG_LEVEL=warn
   LOG_FILE=/var/log/pdf-toolkit/app.log
   ```

3. **File Storage**
   - Use AWS S3 or Google Cloud Storage for file storage
   - Implement CDN for file downloads

4. **Database (Optional)**
   - Add Redis for session management
   - Use PostgreSQL for user management

### Frontend Optimizations

1. **Build Optimization**
   ```bash
   npm run build
   ```

2. **Nginx Configuration**
   Create `nginx.conf`:
   ```nginx
   events {
     worker_connections 1024;
   }
   
   http {
     include /etc/nginx/mime.types;
     default_type application/octet-stream;
     
     gzip on;
     gzip_types text/plain text/css application/json application/javascript;
     
     server {
       listen 80;
       server_name localhost;
       
       location / {
         root /usr/share/nginx/html;
         index index.html index.htm;
         try_files $uri $uri/ /index.html;
       }
       
       location /api {
         proxy_pass http://backend:3001;
         proxy_set_header Host $host;
         proxy_set_header X-Real-IP $remote_addr;
       }
     }
   }
   ```

## 🔒 Security Considerations

### Backend Security

1. **Environment Variables**
   - Never commit `.env` files
   - Use platform-specific secret management

2. **File Upload Security**
   - Implement virus scanning
   - Add file type validation
   - Set strict file size limits

3. **Rate Limiting**
   - Configure appropriate limits for production
   - Use Redis for distributed rate limiting

### Frontend Security

1. **Content Security Policy**
   - Configure CSP headers
   - Restrict external resources

2. **HTTPS**
   - Always use HTTPS in production
   - Configure HSTS headers

## 📊 Monitoring & Logging

### Backend Monitoring

1. **Health Checks**
   - Use `/api/health` endpoint
   - Monitor response times

2. **Error Tracking**
   - Integrate with Sentry or similar
   - Monitor error rates

3. **Performance Monitoring**
   - Track file processing times
   - Monitor memory usage

### Frontend Monitoring

1. **Analytics**
   - Integrate Google Analytics
   - Track user interactions

2. **Error Monitoring**
   - Use Sentry for frontend errors
   - Monitor API call failures

## 🔄 CI/CD Pipeline

### GitHub Actions Example

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy PDF Toolkit

on:
  push:
    branches: [main]

jobs:
  deploy-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Install dependencies
        run: cd backend && npm ci
      - name: Run tests
        run: cd backend && npm test
      - name: Deploy to Railway
        run: |
          npm install -g @railway/cli
          cd backend && railway up
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}

  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Install dependencies
        run: npm ci
      - name: Build
        run: npm run build
      - name: Deploy to Vercel
        run: vercel --prod
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
```

## 🆘 Troubleshooting

### Common Deployment Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are installed
   - Check for TypeScript errors

2. **Environment Variables**
   - Ensure all required variables are set
   - Check variable names and values

3. **File Upload Issues**
   - Verify file size limits
   - Check disk space on server
   - Ensure upload directories exist

4. **CORS Errors**
   - Verify FRONTEND_URL is correctly set
   - Check CORS configuration

### Performance Issues

1. **Slow File Processing**
   - Increase server resources
   - Optimize PDF processing algorithms
   - Implement file processing queues

2. **Memory Issues**
   - Monitor memory usage
   - Implement file cleanup
   - Use streaming for large files

For additional support, check the main README.md troubleshooting section.
