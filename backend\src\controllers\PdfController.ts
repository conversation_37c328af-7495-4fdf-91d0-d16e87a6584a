import { Request, Response } from 'express';
import { PdfService } from '../services/PdfService';
import { ConversionService } from '../services/ConversionService';
import logger from '../utils/logger';
import { ApiResponse } from '../types';

export class PdfController {
  
  /**
   * Merge multiple PDF files
   */
  static async mergePdfs(req: Request, res: Response) {
    try {
      const files = req.files as Express.Multer.File[];
      
      if (!files || files.length < 2) {
        return res.status(400).json({
          success: false,
          error: 'At least 2 PDF files are required for merging'
        } as ApiResponse);
      }
      
      const result = await PdfService.mergePdfs({
        files,
        outputName: req.body.outputName || `merged_${Date.now()}.pdf`
      });
      
      res.json({
        success: true,
        data: {
          fileId: result.filename,
          originalName: result.originalName,
          size: result.size,
          downloadUrl: `/api/download/${result.filename}`
        },
        message: 'PDF files merged successfully'
      } as ApiResponse);
      
    } catch (error) {
      logger.error('Merge PDFs error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to merge PDF files'
      } as ApiResponse);
    }
  }
  
  /**
   * Split PDF file
   */
  static async splitPdf(req: Request, res: Response) {
    try {
      const file = req.file as Express.Multer.File;
      
      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'PDF file is required'
        } as ApiResponse);
      }
      
      const { splitType, pageRange, specificPages } = req.body;
      
      const results = await PdfService.splitPdf({
        file,
        splitType,
        pageRange,
        specificPages
      });
      
      res.json({
        success: true,
        data: {
          files: results.map(result => ({
            fileId: result.filename,
            originalName: result.originalName,
            size: result.size,
            downloadUrl: `/api/download/${result.filename}`
          }))
        },
        message: `PDF split into ${results.length} files successfully`
      } as ApiResponse);
      
    } catch (error) {
      logger.error('Split PDF error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to split PDF file'
      } as ApiResponse);
    }
  }
  
  /**
   * Compress PDF file
   */
  static async compressPdf(req: Request, res: Response) {
    try {
      const file = req.file as Express.Multer.File;
      
      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'PDF file is required'
        } as ApiResponse);
      }
      
      const { compressionLevel } = req.body;
      
      const result = await PdfService.compressPdf({
        file,
        compressionLevel
      });
      
      const compressionRatio = ((file.size - result.size) / file.size * 100).toFixed(1);
      
      res.json({
        success: true,
        data: {
          fileId: result.filename,
          originalName: result.originalName,
          originalSize: file.size,
          compressedSize: result.size,
          compressionRatio: `${compressionRatio}%`,
          downloadUrl: `/api/download/${result.filename}`
        },
        message: 'PDF compressed successfully'
      } as ApiResponse);
      
    } catch (error) {
      logger.error('Compress PDF error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to compress PDF file'
      } as ApiResponse);
    }
  }
  
  /**
   * Rotate PDF pages
   */
  static async rotatePdf(req: Request, res: Response) {
    try {
      const file = req.file as Express.Multer.File;
      
      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'PDF file is required'
        } as ApiResponse);
      }
      
      const { rotation, pages } = req.body;
      
      const result = await PdfService.rotatePdf(file, rotation, pages);
      
      res.json({
        success: true,
        data: {
          fileId: result.filename,
          originalName: result.originalName,
          size: result.size,
          downloadUrl: `/api/download/${result.filename}`
        },
        message: 'PDF rotated successfully'
      } as ApiResponse);
      
    } catch (error) {
      logger.error('Rotate PDF error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to rotate PDF file'
      } as ApiResponse);
    }
  }
  
  /**
   * Convert PDF to images
   */
  static async pdfToImages(req: Request, res: Response) {
    try {
      const file = req.file as Express.Multer.File;
      
      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'PDF file is required'
        } as ApiResponse);
      }
      
      const { outputFormat = 'jpg', quality = 'medium' } = req.body;
      
      const results = await ConversionService.pdfToImage({
        file,
        outputFormat,
        quality
      });
      
      res.json({
        success: true,
        data: {
          files: results.map(result => ({
            fileId: result.filename,
            originalName: result.originalName,
            size: result.size,
            downloadUrl: `/api/download/${result.filename}`
          }))
        },
        message: `PDF converted to ${results.length} images successfully`
      } as ApiResponse);
      
    } catch (error) {
      logger.error('PDF to images error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to convert PDF to images'
      } as ApiResponse);
    }
  }
  
  /**
   * Convert PDF to Word
   */
  static async pdfToWord(req: Request, res: Response) {
    try {
      const file = req.file as Express.Multer.File;
      
      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'PDF file is required'
        } as ApiResponse);
      }
      
      const result = await ConversionService.pdfToWord(file);
      
      res.json({
        success: true,
        data: {
          fileId: result.filename,
          originalName: result.originalName,
          size: result.size,
          downloadUrl: `/api/download/${result.filename}`
        },
        message: 'PDF converted to Word successfully'
      } as ApiResponse);
      
    } catch (error) {
      logger.error('PDF to Word error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to convert PDF to Word'
      } as ApiResponse);
    }
  }
  
  /**
   * Convert Word to PDF
   */
  static async wordToPdf(req: Request, res: Response) {
    try {
      const file = req.file as Express.Multer.File;
      
      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'Word file is required'
        } as ApiResponse);
      }
      
      const result = await ConversionService.wordToPdf(file);
      
      res.json({
        success: true,
        data: {
          fileId: result.filename,
          originalName: result.originalName,
          size: result.size,
          downloadUrl: `/api/download/${result.filename}`
        },
        message: 'Word document converted to PDF successfully'
      } as ApiResponse);
      
    } catch (error) {
      logger.error('Word to PDF error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to convert Word to PDF'
      } as ApiResponse);
    }
  }
  
  /**
   * Convert Excel to PDF
   */
  static async excelToPdf(req: Request, res: Response) {
    try {
      const file = req.file as Express.Multer.File;
      
      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'Excel file is required'
        } as ApiResponse);
      }
      
      const result = await ConversionService.excelToPdf(file);
      
      res.json({
        success: true,
        data: {
          fileId: result.filename,
          originalName: result.originalName,
          size: result.size,
          downloadUrl: `/api/download/${result.filename}`
        },
        message: 'Excel file converted to PDF successfully'
      } as ApiResponse);
      
    } catch (error) {
      logger.error('Excel to PDF error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to convert Excel to PDF'
      } as ApiResponse);
    }
  }
  
  /**
   * Convert PDF to Excel
   */
  static async pdfToExcel(req: Request, res: Response) {
    try {
      const file = req.file as Express.Multer.File;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'PDF file is required'
        } as ApiResponse);
      }

      const result = await ConversionService.pdfToExcel(file);

      res.json({
        success: true,
        data: {
          fileId: result.filename,
          originalName: result.originalName,
          size: result.size,
          downloadUrl: `/api/download/${result.filename}`
        },
        message: 'PDF converted to Excel successfully'
      } as ApiResponse);

    } catch (error) {
      logger.error('PDF to Excel error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to convert PDF to Excel'
      } as ApiResponse);
    }
  }

  /**
   * Convert PDF to PowerPoint
   */
  static async pdfToPowerPoint(req: Request, res: Response) {
    try {
      const file = req.file as Express.Multer.File;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'PDF file is required'
        } as ApiResponse);
      }

      const result = await ConversionService.pdfToPowerPoint(file);

      res.json({
        success: true,
        data: {
          fileId: result.filename,
          originalName: result.originalName,
          size: result.size,
          downloadUrl: `/api/download/${result.filename}`
        },
        message: 'PDF converted to PowerPoint successfully'
      } as ApiResponse);

    } catch (error) {
      logger.error('PDF to PowerPoint error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to convert PDF to PowerPoint'
      } as ApiResponse);
    }
  }

  /**
   * Convert PowerPoint to PDF
   */
  static async powerPointToPdf(req: Request, res: Response) {
    try {
      const file = req.file as Express.Multer.File;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'PowerPoint file is required'
        } as ApiResponse);
      }

      const result = await ConversionService.powerPointToPdf(file);

      res.json({
        success: true,
        data: {
          fileId: result.filename,
          originalName: result.originalName,
          size: result.size,
          downloadUrl: `/api/download/${result.filename}`
        },
        message: 'PowerPoint converted to PDF successfully'
      } as ApiResponse);

    } catch (error) {
      logger.error('PowerPoint to PDF error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to convert PowerPoint to PDF'
      } as ApiResponse);
    }
  }

  /**
   * Convert images to PDF
   */
  static async imagesToPdf(req: Request, res: Response) {
    try {
      const files = req.files as Express.Multer.File[];
      
      if (!files || files.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'At least one image file is required'
        } as ApiResponse);
      }
      
      const result = await ConversionService.imagesToPdf(files);
      
      res.json({
        success: true,
        data: {
          fileId: result.filename,
          originalName: result.originalName,
          size: result.size,
          downloadUrl: `/api/download/${result.filename}`
        },
        message: `${files.length} images converted to PDF successfully`
      } as ApiResponse);
      
    } catch (error) {
      logger.error('Images to PDF error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to convert images to PDF'
      } as ApiResponse);
    }
  }
}
