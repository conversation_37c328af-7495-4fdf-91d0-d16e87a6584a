import fs from 'fs';
import path from 'path';
import { PDFDocument } from 'pdf-lib';
import logger from '../utils/logger';
import { ErrorDetails } from '../types';

export enum ErrorCode {
  // File-related errors
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  FILE_CORRUPTED = 'FILE_CORRUPTED',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  
  // PDF-specific errors
  PDF_CORRUPTED = 'PDF_CORRUPTED',
  PDF_ENCRYPTED = 'PDF_ENCRYPTED',
  PDF_INVALID_PAGES = 'PDF_INVALID_PAGES',
  PDF_PROCESSING_FAILED = 'PDF_PROCESSING_FAILED',
  
  // Memory and resource errors
  MEMORY_LIMIT_EXCEEDED = 'MEMORY_LIMIT_EXCEEDED',
  DISK_SPACE_INSUFFICIENT = 'DISK_SPACE_INSUFFICIENT',
  PROCESSING_TIMEOUT = 'PROCESSING_TIMEOUT',
  
  // Service errors
  EXTERNAL_SERVICE_UNAVAILABLE = 'EXTERNAL_SERVICE_UNAVAILABLE',
  GHOSTSCRIPT_NOT_FOUND = 'GHOSTSCRIPT_NOT_FOUND',
  LIBREOFFICE_NOT_FOUND = 'LIBREOFFICE_NOT_FOUND',
  
  // Validation errors
  INVALID_PARAMETERS = 'INVALID_PARAMETERS',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  
  // General errors
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface ErrorContext {
  operation: string;
  fileInfo?: {
    name: string;
    size: number;
    type: string;
    path?: string;
  };
  parameters?: Record<string, any>;
  stackTrace?: string;
  timestamp: Date;
}

export class ApplicationError extends Error {
  public readonly code: ErrorCode;
  public readonly context: ErrorContext;
  public readonly isRetryable: boolean;
  public readonly userMessage: string;

  constructor(
    code: ErrorCode,
    message: string,
    context: ErrorContext,
    isRetryable: boolean = false,
    userMessage?: string
  ) {
    super(message);
    this.name = 'ApplicationError';
    this.code = code;
    this.context = context;
    this.isRetryable = isRetryable;
    this.userMessage = userMessage || this.getDefaultUserMessage(code);
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ApplicationError);
    }
    
    this.context.stackTrace = this.stack;
  }

  private getDefaultUserMessage(code: ErrorCode): string {
    const messages: Record<ErrorCode, string> = {
      [ErrorCode.FILE_NOT_FOUND]: 'The specified file could not be found.',
      [ErrorCode.FILE_TOO_LARGE]: 'The file is too large to process.',
      [ErrorCode.FILE_CORRUPTED]: 'The file appears to be corrupted or damaged.',
      [ErrorCode.INVALID_FILE_TYPE]: 'The file type is not supported for this operation.',
      [ErrorCode.INSUFFICIENT_PERMISSIONS]: 'Insufficient permissions to access the file.',
      
      [ErrorCode.PDF_CORRUPTED]: 'The PDF file is corrupted and cannot be processed.',
      [ErrorCode.PDF_ENCRYPTED]: 'The PDF file is password-protected. Please provide the password or use an unprotected file.',
      [ErrorCode.PDF_INVALID_PAGES]: 'The specified page range is invalid for this PDF.',
      [ErrorCode.PDF_PROCESSING_FAILED]: 'Failed to process the PDF file.',
      
      [ErrorCode.MEMORY_LIMIT_EXCEEDED]: 'The operation requires more memory than available. Please try with smaller files.',
      [ErrorCode.DISK_SPACE_INSUFFICIENT]: 'Insufficient disk space to complete the operation.',
      [ErrorCode.PROCESSING_TIMEOUT]: 'The operation took too long and was cancelled.',
      
      [ErrorCode.EXTERNAL_SERVICE_UNAVAILABLE]: 'An external service required for this operation is currently unavailable.',
      [ErrorCode.GHOSTSCRIPT_NOT_FOUND]: 'PDF compression service is not available.',
      [ErrorCode.LIBREOFFICE_NOT_FOUND]: 'Document conversion service is not available.',
      
      [ErrorCode.INVALID_PARAMETERS]: 'The provided parameters are invalid.',
      [ErrorCode.MISSING_REQUIRED_FIELD]: 'A required field is missing.',
      
      [ErrorCode.INTERNAL_SERVER_ERROR]: 'An internal server error occurred.',
      [ErrorCode.UNKNOWN_ERROR]: 'An unknown error occurred.'
    };
    
    return messages[code] || 'An error occurred while processing your request.';
  }

  toJSON(): ErrorDetails {
    return {
      code: this.code,
      message: this.userMessage,
      details: {
        technicalMessage: this.message,
        context: this.context,
        isRetryable: this.isRetryable
      },
      timestamp: this.context.timestamp,
      operation: this.context.operation,
      fileInfo: this.context.fileInfo
    };
  }
}

export class ErrorHandler {
  
  /**
   * Validate if a file exists and is accessible
   */
  static async validateFileAccess(filePath: string): Promise<void> {
    try {
      await fs.promises.access(filePath, fs.constants.F_OK | fs.constants.R_OK);
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
        throw new ApplicationError(
          ErrorCode.FILE_NOT_FOUND,
          `File not found: ${filePath}`,
          {
            operation: 'file-validation',
            fileInfo: { name: path.basename(filePath), size: 0, type: 'unknown', path: filePath },
            timestamp: new Date()
          }
        );
      } else if ((error as NodeJS.ErrnoException).code === 'EACCES') {
        throw new ApplicationError(
          ErrorCode.INSUFFICIENT_PERMISSIONS,
          `Insufficient permissions to access file: ${filePath}`,
          {
            operation: 'file-validation',
            fileInfo: { name: path.basename(filePath), size: 0, type: 'unknown', path: filePath },
            timestamp: new Date()
          }
        );
      }
      throw error;
    }
  }

  /**
   * Validate PDF file integrity
   */
  static async validatePdfFile(filePath: string): Promise<void> {
    try {
      await this.validateFileAccess(filePath);
      
      const pdfBytes = fs.readFileSync(filePath);
      const stats = fs.statSync(filePath);
      
      // Basic PDF header check
      if (!pdfBytes.subarray(0, 4).equals(Buffer.from('%PDF'))) {
        throw new ApplicationError(
          ErrorCode.PDF_CORRUPTED,
          'File does not have a valid PDF header',
          {
            operation: 'pdf-validation',
            fileInfo: {
              name: path.basename(filePath),
              size: stats.size,
              type: 'application/pdf',
              path: filePath
            },
            timestamp: new Date()
          }
        );
      }
      
      // Try to load with pdf-lib
      try {
        const pdf = await PDFDocument.load(pdfBytes);
        const pageCount = pdf.getPageCount();
        
        if (pageCount === 0) {
          throw new ApplicationError(
            ErrorCode.PDF_CORRUPTED,
            'PDF file contains no pages',
            {
              operation: 'pdf-validation',
              fileInfo: {
                name: path.basename(filePath),
                size: stats.size,
                type: 'application/pdf',
                path: filePath
              },
              timestamp: new Date()
            }
          );
        }
        
        logger.debug(`PDF validation successful: ${pageCount} pages`);
        
      } catch (pdfError) {
        const errorMessage = pdfError instanceof Error ? pdfError.message : 'Unknown PDF error';
        
        // Check for specific PDF errors
        if (errorMessage.includes('encrypted') || errorMessage.includes('password')) {
          throw new ApplicationError(
            ErrorCode.PDF_ENCRYPTED,
            'PDF file is password-protected',
            {
              operation: 'pdf-validation',
              fileInfo: {
                name: path.basename(filePath),
                size: stats.size,
                type: 'application/pdf',
                path: filePath
              },
              timestamp: new Date()
            }
          );
        }
        
        throw new ApplicationError(
          ErrorCode.PDF_CORRUPTED,
          `PDF file is corrupted: ${errorMessage}`,
          {
            operation: 'pdf-validation',
            fileInfo: {
              name: path.basename(filePath),
              size: stats.size,
              type: 'application/pdf',
              path: filePath
            },
            timestamp: new Date()
          }
        );
      }
      
    } catch (error) {
      if (error instanceof ApplicationError) {
        throw error;
      }
      
      throw new ApplicationError(
        ErrorCode.FILE_CORRUPTED,
        `File validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        {
          operation: 'pdf-validation',
          fileInfo: {
            name: path.basename(filePath),
            size: 0,
            type: 'application/pdf',
            path: filePath
          },
          timestamp: new Date()
        }
      );
    }
  }

  /**
   * Validate page range for PDF operations
   */
  static validatePageRange(pageRange: string, totalPages: number): number[] {
    try {
      const pages: number[] = [];
      const ranges = pageRange.split(',').map(r => r.trim());
      
      for (const range of ranges) {
        if (range.includes('-')) {
          const [start, end] = range.split('-').map(p => parseInt(p.trim()));
          
          if (isNaN(start) || isNaN(end) || start < 1 || end > totalPages || start > end) {
            throw new ApplicationError(
              ErrorCode.PDF_INVALID_PAGES,
              `Invalid page range: ${range}`,
              {
                operation: 'page-validation',
                parameters: { pageRange, totalPages },
                timestamp: new Date()
              }
            );
          }
          
          for (let i = start; i <= end; i++) {
            pages.push(i);
          }
        } else {
          const page = parseInt(range);
          
          if (isNaN(page) || page < 1 || page > totalPages) {
            throw new ApplicationError(
              ErrorCode.PDF_INVALID_PAGES,
              `Invalid page number: ${range}`,
              {
                operation: 'page-validation',
                parameters: { pageRange, totalPages },
                timestamp: new Date()
              }
            );
          }
          
          pages.push(page);
        }
      }
      
      return [...new Set(pages)].sort((a, b) => a - b);
      
    } catch (error) {
      if (error instanceof ApplicationError) {
        throw error;
      }
      
      throw new ApplicationError(
        ErrorCode.INVALID_PARAMETERS,
        `Page range validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        {
          operation: 'page-validation',
          parameters: { pageRange, totalPages },
          timestamp: new Date()
        }
      );
    }
  }

  /**
   * Create error from unknown error
   */
  static createError(
    error: unknown,
    operation: string,
    fileInfo?: { name: string; size: number; type: string; path?: string }
  ): ApplicationError {
    if (error instanceof ApplicationError) {
      return error;
    }
    
    const message = error instanceof Error ? error.message : 'Unknown error occurred';
    const code = this.determineErrorCode(message);
    
    return new ApplicationError(
      code,
      message,
      {
        operation,
        fileInfo,
        timestamp: new Date()
      },
      this.isRetryableError(code)
    );
  }

  /**
   * Determine error code from error message
   */
  private static determineErrorCode(message: string): ErrorCode {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('memory') || lowerMessage.includes('heap')) {
      return ErrorCode.MEMORY_LIMIT_EXCEEDED;
    }
    if (lowerMessage.includes('timeout')) {
      return ErrorCode.PROCESSING_TIMEOUT;
    }
    if (lowerMessage.includes('disk') || lowerMessage.includes('space')) {
      return ErrorCode.DISK_SPACE_INSUFFICIENT;
    }
    if (lowerMessage.includes('ghostscript')) {
      return ErrorCode.GHOSTSCRIPT_NOT_FOUND;
    }
    if (lowerMessage.includes('libreoffice') || lowerMessage.includes('soffice')) {
      return ErrorCode.LIBREOFFICE_NOT_FOUND;
    }
    if (lowerMessage.includes('encrypted') || lowerMessage.includes('password')) {
      return ErrorCode.PDF_ENCRYPTED;
    }
    if (lowerMessage.includes('corrupted') || lowerMessage.includes('invalid pdf')) {
      return ErrorCode.PDF_CORRUPTED;
    }
    
    return ErrorCode.UNKNOWN_ERROR;
  }

  /**
   * Check if error is retryable
   */
  private static isRetryableError(code: ErrorCode): boolean {
    const retryableCodes = [
      ErrorCode.MEMORY_LIMIT_EXCEEDED,
      ErrorCode.PROCESSING_TIMEOUT,
      ErrorCode.EXTERNAL_SERVICE_UNAVAILABLE,
      ErrorCode.INTERNAL_SERVER_ERROR
    ];
    
    return retryableCodes.includes(code);
  }
}
