#!/bin/bash

# PDF Toolkit System Test Script
# This script tests the complete system functionality

echo "🧪 PDF Toolkit System Test"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ $2${NC}"
        ((TESTS_FAILED++))
    fi
}

# Function to check if a service is running
check_service() {
    local url=$1
    local name=$2
    
    if curl -s "$url" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to test API endpoint
test_api_endpoint() {
    local url=$1
    local method=$2
    local expected_status=$3
    local description=$4
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" "$url")
        status_code="${response: -3}"
    else
        status_code=$(curl -s -o /dev/null -w "%{http_code}" -X "$method" "$url")
    fi
    
    if [ "$status_code" = "$expected_status" ]; then
        print_result 0 "$description"
    else
        print_result 1 "$description (Expected: $expected_status, Got: $status_code)"
    fi
}

echo "🔍 Checking Prerequisites..."

# Check Node.js
if command -v node &> /dev/null; then
    NODE_VERSION=$(node -v)
    echo -e "${GREEN}✅ Node.js installed: $NODE_VERSION${NC}"
else
    echo -e "${RED}❌ Node.js not found${NC}"
    exit 1
fi

# Check npm
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm -v)
    echo -e "${GREEN}✅ npm installed: $NPM_VERSION${NC}"
else
    echo -e "${RED}❌ npm not found${NC}"
    exit 1
fi

echo ""
echo "🏗️ Checking Project Structure..."

# Check if backend directory exists
if [ -d "backend" ]; then
    print_result 0 "Backend directory exists"
else
    print_result 1 "Backend directory missing"
fi

# Check if backend package.json exists
if [ -f "backend/package.json" ]; then
    print_result 0 "Backend package.json exists"
else
    print_result 1 "Backend package.json missing"
fi

# Check if frontend package.json exists
if [ -f "package.json" ]; then
    print_result 0 "Frontend package.json exists"
else
    print_result 1 "Frontend package.json missing"
fi

# Check if environment files exist
if [ -f "backend/.env.example" ]; then
    print_result 0 "Backend .env.example exists"
else
    print_result 1 "Backend .env.example missing"
fi

if [ -f ".env.example" ]; then
    print_result 0 "Frontend .env.example exists"
else
    print_result 1 "Frontend .env.example missing"
fi

echo ""
echo "📦 Checking Dependencies..."

# Check backend dependencies
cd backend
if [ -d "node_modules" ]; then
    print_result 0 "Backend dependencies installed"
else
    echo -e "${YELLOW}⚠️ Installing backend dependencies...${NC}"
    npm install
    if [ $? -eq 0 ]; then
        print_result 0 "Backend dependencies installed successfully"
    else
        print_result 1 "Failed to install backend dependencies"
    fi
fi

# Check if backend builds successfully
echo -e "${YELLOW}🔨 Building backend...${NC}"
npm run build
if [ $? -eq 0 ]; then
    print_result 0 "Backend builds successfully"
else
    print_result 1 "Backend build failed"
fi

cd ..

# Check frontend dependencies
if [ -d "node_modules" ]; then
    print_result 0 "Frontend dependencies installed"
else
    echo -e "${YELLOW}⚠️ Installing frontend dependencies...${NC}"
    npm install
    if [ $? -eq 0 ]; then
        print_result 0 "Frontend dependencies installed successfully"
    else
        print_result 1 "Failed to install frontend dependencies"
    fi
fi

echo ""
echo "🧪 Running Unit Tests..."

# Run backend tests
cd backend
if npm test > /dev/null 2>&1; then
    print_result 0 "Backend unit tests pass"
else
    print_result 1 "Backend unit tests failed"
fi

cd ..

# Run frontend tests (if they exist)
if [ -f "src/__tests__" ] || grep -q "test" package.json; then
    if npm test > /dev/null 2>&1; then
        print_result 0 "Frontend tests pass"
    else
        print_result 1 "Frontend tests failed"
    fi
else
    echo -e "${YELLOW}⚠️ No frontend tests found${NC}"
fi

echo ""
echo "🚀 Testing Services..."

# Check if backend is running
check_service "http://localhost:3001/api/health" "Backend"
if [ $? -eq 0 ]; then
    print_result 0 "Backend service is running"
    
    # Test API endpoints
    test_api_endpoint "http://localhost:3001/api/health" "GET" "200" "Health check endpoint"
    
else
    print_result 1 "Backend service is not running"
    echo -e "${YELLOW}💡 Start the backend with: cd backend && npm run dev${NC}"
fi

# Check if frontend is running
check_service "http://localhost:5173" "Frontend"
if [ $? -eq 0 ]; then
    print_result 0 "Frontend service is running"
else
    print_result 1 "Frontend service is not running"
    echo -e "${YELLOW}💡 Start the frontend with: npm run dev${NC}"
fi

echo ""
echo "📁 Checking File Structure..."

# Check important files
important_files=(
    "README.md"
    "DEPLOYMENT.md"
    "backend/src/server.ts"
    "backend/src/services/PdfService.ts"
    "backend/src/services/ConversionService.ts"
    "backend/src/controllers/PdfController.ts"
    "backend/src/routes/pdf.ts"
    "src/services/api.ts"
    "src/pages/MergePDF.tsx"
    "src/pages/SplitPDF.tsx"
    "src/pages/CompressPDF.tsx"
)

for file in "${important_files[@]}"; do
    if [ -f "$file" ]; then
        print_result 0 "File exists: $file"
    else
        print_result 1 "File missing: $file"
    fi
done

echo ""
echo "📊 Test Summary"
echo "==============="
echo -e "${GREEN}Tests Passed: $TESTS_PASSED${NC}"
echo -e "${RED}Tests Failed: $TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed! Your PDF Toolkit is ready to use.${NC}"
    echo ""
    echo "🚀 Quick Start:"
    echo "1. Start backend: cd backend && npm run dev"
    echo "2. Start frontend: npm run dev"
    echo "3. Open http://localhost:5173 in your browser"
    exit 0
else
    echo -e "${RED}⚠️ Some tests failed. Please check the issues above.${NC}"
    exit 1
fi
