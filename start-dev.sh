#!/bin/bash

# PDF Toolkit Development Startup Script
# This script starts both the backend and frontend development servers

echo "🚀 Starting PDF Toolkit Development Environment..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18.0 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18.0 or higher is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Check if ports are available
if check_port 3001; then
    echo "❌ Port 3001 is already in use. Please stop the service using this port."
    exit 1
fi

if check_port 5173; then
    echo "❌ Port 5173 is already in use. Please stop the service using this port."
    exit 1
fi

# Setup backend
echo "📦 Setting up backend..."
cd backend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📥 Installing backend dependencies..."
    npm install
fi

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "⚙️ Creating backend environment configuration..."
    cp .env.example .env
    echo "✅ Backend .env file created. You can modify it if needed."
fi

# Build backend
echo "🔨 Building backend..."
npm run build

# Start backend in background
echo "🚀 Starting backend server on port 3001..."
npm run dev &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Setup frontend
echo "📦 Setting up frontend..."
cd ..

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📥 Installing frontend dependencies..."
    npm install
fi

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "⚙️ Creating frontend environment configuration..."
    cp .env.example .env
    echo "✅ Frontend .env file created. You can modify it if needed."
fi

# Start frontend
echo "🚀 Starting frontend server on port 5173..."
npm run dev &
FRONTEND_PID=$!

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ Servers stopped."
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

echo ""
echo "🎉 PDF Toolkit is starting up!"
echo "📱 Frontend: http://localhost:5173"
echo "🔧 Backend API: http://localhost:3001/api"
echo "📊 Health Check: http://localhost:3001/api/health"
echo ""
echo "Press Ctrl+C to stop both servers"
echo ""

# Wait for both processes
wait
