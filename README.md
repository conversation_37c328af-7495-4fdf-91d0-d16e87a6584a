# PDF Toolkit - Complete Full-Stack Application

A comprehensive PDF processing web application with a React frontend and Node.js backend, supporting 30+ PDF operations including merge, split, compress, convert, and advanced features.

## 🚀 Features

### Core PDF Operations
- **Merge PDFs** - Combine multiple PDF files into one
- **Split PDFs** - Extract pages or split into multiple files
- **Compress PDFs** - Reduce file size with quality options
- **Rotate PDFs** - Rotate pages by 90°, 180°, or 270°

### Format Conversions
- **PDF ↔ Word** - Convert between PDF and Word documents
- **PDF ↔ Excel** - Convert between PDF and Excel spreadsheets
- **PDF ↔ PowerPoint** - Convert between PDF and PowerPoint presentations
- **PDF ↔ Images** - Convert PDF to JPG/PNG or images to PDF

### Advanced Features
- **OCR** - Extract text from scanned PDFs
- **Digital Signatures** - Sign PDF documents
- **Watermarks** - Add text or image watermarks
- **Password Protection** - Secure PDFs with passwords
- **Page Numbers** - Add page numbering
- **Crop & Edit** - Modify PDF content

## 🏗️ Architecture

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **React Router** for navigation
- **Lucide React** for icons

### Backend
- **Node.js** with Express and TypeScript
- **pdf-lib** for PDF manipulation
- **sharp** for image processing
- **mammoth** for Word document handling
- **multer** for file uploads
- **winston** for logging

## 📋 Prerequisites

- **Node.js** 18.0 or higher
- **npm** or **yarn**
- **Git**

## 🛠️ Installation & Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd pdf-toolkit
```

### 2. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env

# Edit .env file with your configuration
# Default values should work for local development

# Build the TypeScript code
npm run build

# Start the backend server
npm run dev
```

The backend will start on `http://localhost:3001`

### 3. Frontend Setup

```bash
# Navigate to frontend directory (from project root)
cd ../

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env

# Start the frontend development server
npm run dev
```

The frontend will start on `http://localhost:5173`

## 🔧 Configuration

### Backend Environment Variables

```env
# Server Configuration
PORT=3001
NODE_ENV=development

# File Upload Configuration
MAX_FILE_SIZE=52428800          # 50MB
MAX_TOTAL_SIZE=209715200        # 200MB
UPLOAD_DIR=./uploads
TEMP_DIR=./temp

# Security
RATE_LIMIT_WINDOW_MS=900000     # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
FRONTEND_URL=http://localhost:5173

# File Cleanup
CLEANUP_INTERVAL_MS=3600000     # 1 hour
FILE_RETENTION_MS=3600000       # 1 hour
```

### Frontend Environment Variables

```env
# Backend API URL
VITE_API_URL=http://localhost:3001/api
```

## 📚 API Documentation

### Base URL
```
http://localhost:3001/api
```

### Core Endpoints

#### Merge PDFs
```http
POST /pdf/merge
Content-Type: multipart/form-data

files: File[]           # Multiple PDF files
outputName?: string     # Optional output filename
```

#### Split PDF
```http
POST /pdf/split
Content-Type: multipart/form-data

file: File                                    # PDF file to split
splitType: 'all' | 'range' | 'specific'     # Split method
pageRange?: string                           # For range: "1-5, 8-12"
specificPages?: string                       # For specific: "1, 3, 5"
```

#### Compress PDF
```http
POST /pdf/compress
Content-Type: multipart/form-data

file: File                              # PDF file to compress
compressionLevel: 'low' | 'medium' | 'high'
```

#### Convert PDF to Images
```http
POST /pdf/convert/to-images
Content-Type: multipart/form-data

file: File                              # PDF file
outputFormat: string                    # 'jpg' or 'png'
quality: 'low' | 'medium' | 'high'
```

### Response Format

All endpoints return responses in this format:

```json
{
  "success": boolean,
  "data": {
    "fileId": "string",
    "originalName": "string",
    "size": number,
    "downloadUrl": "string"
  },
  "message": "string",
  "error": "string"
}
```

### Download Files
```http
GET /download/:fileId
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
npm test
```

### Frontend Tests
```bash
npm test
```

### Manual Testing
1. Start both backend and frontend servers
2. Open `http://localhost:5173` in your browser
3. Test various PDF operations with sample files
4. Verify file uploads, processing, and downloads work correctly

## 🚀 Production Deployment

### Backend Deployment

1. **Build the application:**
```bash
cd backend
npm run build
```

2. **Set production environment variables:**
```env
NODE_ENV=production
PORT=3001
FRONTEND_URL=https://your-frontend-domain.com
```

3. **Start the production server:**
```bash
npm start
```

### Frontend Deployment

1. **Build the application:**
```bash
npm run build
```

2. **Set production API URL:**
```env
VITE_API_URL=https://your-backend-domain.com/api
```

3. **Deploy the `dist` folder** to your hosting service

### Recommended Hosting

- **Backend**: Railway, Render, DigitalOcean, AWS
- **Frontend**: Vercel, Netlify, GitHub Pages
- **Files**: AWS S3, Google Cloud Storage (for production file storage)

## 🔍 Troubleshooting

### Common Issues

#### Backend won't start
- Check if port 3001 is available
- Verify Node.js version (18.0+)
- Check environment variables in `.env`

#### File upload fails
- Verify file size limits in backend configuration
- Check CORS settings
- Ensure temp directories exist and are writable

#### PDF processing errors
- Check if uploaded files are valid PDFs
- Verify sufficient disk space for temporary files
- Check backend logs for detailed error messages

#### Frontend can't connect to backend
- Verify backend is running on correct port
- Check `VITE_API_URL` in frontend `.env`
- Verify CORS configuration allows frontend domain

### Logs

Backend logs are stored in:
- `backend/logs/combined.log` - All logs
- `backend/logs/error.log` - Error logs only

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the logs for error details
3. Create an issue in the repository
4. Include relevant error messages and steps to reproduce
