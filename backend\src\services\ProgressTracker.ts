import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger';
import { ProcessingJob } from '../types';

export interface ProgressUpdate {
  jobId: string;
  progress: number; // 0-100
  status: 'pending' | 'processing' | 'completed' | 'failed';
  message?: string;
  currentStep?: string;
  totalSteps?: number;
  currentStepIndex?: number;
  estimatedTimeRemaining?: number; // seconds
  result?: any;
  error?: string;
}

export interface JobStep {
  name: string;
  weight: number; // Relative weight for progress calculation
}

export class ProgressTracker extends EventEmitter {
  private jobs: Map<string, ProcessingJob> = new Map();
  private jobSteps: Map<string, JobStep[]> = new Map();
  private jobStartTimes: Map<string, number> = new Map();
  private stepStartTimes: Map<string, number> = new Map();

  /**
   * Create a new job and return its ID
   */
  createJob(type: string, steps?: JobStep[]): string {
    const jobId = uuidv4();
    const now = new Date();
    
    const job: ProcessingJob = {
      id: jobId,
      type,
      status: 'pending',
      progress: 0,
      createdAt: now,
      updatedAt: now
    };
    
    this.jobs.set(jobId, job);
    this.jobStartTimes.set(jobId, Date.now());
    
    if (steps) {
      this.jobSteps.set(jobId, steps);
    }
    
    logger.info(`Created job ${jobId} of type ${type}`);
    this.emitProgress(jobId);
    
    return jobId;
  }

  /**
   * Update job progress
   */
  updateProgress(
    jobId: string, 
    progress: number, 
    message?: string,
    currentStep?: string,
    currentStepIndex?: number
  ): void {
    const job = this.jobs.get(jobId);
    if (!job) {
      logger.warn(`Job ${jobId} not found for progress update`);
      return;
    }
    
    job.progress = Math.min(100, Math.max(0, progress));
    job.updatedAt = new Date();
    
    if (progress > 0 && job.status === 'pending') {
      job.status = 'processing';
    }
    
    // Calculate estimated time remaining
    let estimatedTimeRemaining: number | undefined;
    if (progress > 0 && progress < 100) {
      const elapsed = Date.now() - this.jobStartTimes.get(jobId)!;
      const totalEstimated = (elapsed / progress) * 100;
      estimatedTimeRemaining = Math.round((totalEstimated - elapsed) / 1000);
    }
    
    const steps = this.jobSteps.get(jobId);
    const totalSteps = steps?.length;
    
    this.emitProgress(jobId, {
      message,
      currentStep,
      currentStepIndex,
      totalSteps,
      estimatedTimeRemaining
    });
  }

  /**
   * Update progress for a specific step
   */
  updateStepProgress(
    jobId: string,
    stepIndex: number,
    stepProgress: number, // 0-100 for this step
    message?: string
  ): void {
    const steps = this.jobSteps.get(jobId);
    if (!steps || stepIndex >= steps.length) {
      logger.warn(`Invalid step index ${stepIndex} for job ${jobId}`);
      return;
    }
    
    // Calculate overall progress based on step weights
    const totalWeight = steps.reduce((sum, step) => sum + step.weight, 0);
    let overallProgress = 0;
    
    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];
      const stepWeight = step.weight / totalWeight;
      
      if (i < stepIndex) {
        // Completed steps
        overallProgress += stepWeight * 100;
      } else if (i === stepIndex) {
        // Current step
        overallProgress += stepWeight * stepProgress;
      }
      // Future steps contribute 0
    }
    
    this.updateProgress(
      jobId,
      overallProgress,
      message,
      steps[stepIndex].name,
      stepIndex
    );
  }

  /**
   * Mark job as completed
   */
  completeJob(jobId: string, result?: any): void {
    const job = this.jobs.get(jobId);
    if (!job) {
      logger.warn(`Job ${jobId} not found for completion`);
      return;
    }
    
    job.status = 'completed';
    job.progress = 100;
    job.result = result;
    job.updatedAt = new Date();
    
    const elapsed = Date.now() - this.jobStartTimes.get(jobId)!;
    logger.info(`Job ${jobId} completed in ${elapsed}ms`);
    
    this.emitProgress(jobId, { result });
    
    // Clean up after 5 minutes
    setTimeout(() => {
      this.cleanupJob(jobId);
    }, 5 * 60 * 1000);
  }

  /**
   * Mark job as failed
   */
  failJob(jobId: string, error: string): void {
    const job = this.jobs.get(jobId);
    if (!job) {
      logger.warn(`Job ${jobId} not found for failure`);
      return;
    }
    
    job.status = 'failed';
    job.error = error;
    job.updatedAt = new Date();
    
    logger.error(`Job ${jobId} failed: ${error}`);
    this.emitProgress(jobId, { error });
    
    // Clean up after 5 minutes
    setTimeout(() => {
      this.cleanupJob(jobId);
    }, 5 * 60 * 1000);
  }

  /**
   * Get job status
   */
  getJob(jobId: string): ProcessingJob | undefined {
    return this.jobs.get(jobId);
  }

  /**
   * Get all active jobs
   */
  getActiveJobs(): ProcessingJob[] {
    return Array.from(this.jobs.values()).filter(
      job => job.status === 'pending' || job.status === 'processing'
    );
  }

  /**
   * Clean up job data
   */
  private cleanupJob(jobId: string): void {
    this.jobs.delete(jobId);
    this.jobSteps.delete(jobId);
    this.jobStartTimes.delete(jobId);
    this.stepStartTimes.delete(jobId);
    logger.debug(`Cleaned up job ${jobId}`);
  }

  /**
   * Emit progress update event
   */
  private emitProgress(jobId: string, additional?: Partial<ProgressUpdate>): void {
    const job = this.jobs.get(jobId);
    if (!job) return;
    
    const update: ProgressUpdate = {
      jobId,
      progress: job.progress,
      status: job.status,
      result: job.result,
      error: job.error,
      ...additional
    };
    
    this.emit('progress', update);
  }

  /**
   * Start step timing
   */
  startStep(jobId: string, stepIndex: number): void {
    const stepKey = `${jobId}-${stepIndex}`;
    this.stepStartTimes.set(stepKey, Date.now());
  }

  /**
   * End step timing
   */
  endStep(jobId: string, stepIndex: number): void {
    const stepKey = `${jobId}-${stepIndex}`;
    const startTime = this.stepStartTimes.get(stepKey);
    
    if (startTime) {
      const elapsed = Date.now() - startTime;
      logger.debug(`Step ${stepIndex} of job ${jobId} took ${elapsed}ms`);
      this.stepStartTimes.delete(stepKey);
    }
  }

  /**
   * Get job statistics
   */
  getStats(): {
    totalJobs: number;
    activeJobs: number;
    completedJobs: number;
    failedJobs: number;
  } {
    const jobs = Array.from(this.jobs.values());
    
    return {
      totalJobs: jobs.length,
      activeJobs: jobs.filter(j => j.status === 'pending' || j.status === 'processing').length,
      completedJobs: jobs.filter(j => j.status === 'completed').length,
      failedJobs: jobs.filter(j => j.status === 'failed').length
    };
  }
}

// Global progress tracker instance
export const progressTracker = new ProgressTracker();
