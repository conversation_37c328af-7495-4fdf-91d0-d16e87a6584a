export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface FileUploadRequest {
  files: Express.Multer.File[];
  options?: Record<string, any>;
}

export interface ProcessedFile {
  id: string;
  originalName: string;
  filename: string;
  path: string;
  size: number;
  mimeType: string;
  createdAt: Date;
}

export interface PdfMergeOptions {
  files: Express.Multer.File[];
  outputName?: string;
}

export interface PdfSplitOptions {
  file: Express.Multer.File;
  splitType: 'all' | 'range' | 'specific';
  pageRange?: string;
  specificPages?: string;
}

export interface PdfCompressOptions {
  file: Express.Multer.File;
  compressionLevel: 'low' | 'medium' | 'high';
}

export interface PdfConversionOptions {
  file: Express.Multer.File;
  outputFormat: string;
  quality?: 'low' | 'medium' | 'high';
}

export interface PdfProtectionOptions {
  file: Express.Multer.File;
  password: string;
  permissions?: {
    printing?: boolean;
    modifying?: boolean;
    copying?: boolean;
    annotating?: boolean;
  };
}

export interface PdfWatermarkOptions {
  file: Express.Multer.File;
  watermarkText?: string;
  watermarkImage?: Express.Multer.File;
  position: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  opacity: number;
}

export interface OcrOptions {
  file: Express.Multer.File;
  language: 'fr' | 'en' | 'es' | 'de' | 'auto';
}

export interface ProcessingJob {
  id: string;
  type: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  result?: ProcessedFile;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CompressionResult {
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  spaceSaved: number;
  compressionRatioFormatted: string;
  spaceSavedFormatted: string;
}

export interface ErrorDetails {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  operation: string;
  fileInfo?: {
    name: string;
    size: number;
    type: string;
  };
}
