import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { exec } from 'child_process';
import { promisify } from 'util';
import config from '../config';
import logger from '../utils/logger';
import { FileUtils } from '../utils/fileUtils';
import { ProcessedFile } from '../types';

const execAsync = promisify(exec);

export interface ConversionOptions {
  inputPath: string;
  outputPath: string;
  outputFormat: string;
  quality?: 'low' | 'medium' | 'high';
}

export class LibreOfficeService {
  
  /**
   * Check if LibreOffice is available
   */
  static async isLibreOfficeAvailable(): Promise<boolean> {
    try {
      // Try different common LibreOffice commands
      const commands = ['libreoffice', 'soffice', '/usr/bin/libreoffice', '/Applications/LibreOffice.app/Contents/MacOS/soffice'];
      
      for (const cmd of commands) {
        try {
          await execAsync(`${cmd} --version`);
          logger.info(`LibreOffice found: ${cmd}`);
          return true;
        } catch (error) {
          // Continue to next command
        }
      }
      
      logger.warn('LibreOffice not found in common locations');
      return false;
    } catch (error) {
      logger.warn('LibreOffice availability check failed:', error);
      return false;
    }
  }

  /**
   * Get LibreOffice command
   */
  private static async getLibreOfficeCommand(): Promise<string> {
    const commands = ['libreoffice', 'soffice', '/usr/bin/libreoffice', '/Applications/LibreOffice.app/Contents/MacOS/soffice'];
    
    for (const cmd of commands) {
      try {
        await execAsync(`${cmd} --version`);
        return cmd;
      } catch (error) {
        // Continue to next command
      }
    }
    
    throw new Error('LibreOffice not found');
  }

  /**
   * Convert document using LibreOffice headless mode
   */
  static async convertDocument(options: ConversionOptions): Promise<void> {
    try {
      const libreOfficeCmd = await this.getLibreOfficeCommand();
      const outputDir = path.dirname(options.outputPath);
      
      // Ensure output directory exists
      FileUtils.ensureDirectoryExists(outputDir);
      
      // Build LibreOffice command
      const command = [
        libreOfficeCmd,
        '--headless',
        '--convert-to',
        options.outputFormat,
        '--outdir',
        `"${outputDir}"`,
        `"${options.inputPath}"`
      ].join(' ');
      
      logger.info(`Executing LibreOffice conversion: ${command}`);
      
      const { stdout, stderr } = await execAsync(command, {
        timeout: 60000, // 60 second timeout
        maxBuffer: 1024 * 1024 * 10 // 10MB buffer
      });
      
      if (stderr && !stderr.includes('Warning')) {
        logger.warn(`LibreOffice warnings: ${stderr}`);
      }
      
      // LibreOffice creates files with the same base name but different extension
      const inputBaseName = path.basename(options.inputPath, path.extname(options.inputPath));
      const expectedOutputPath = path.join(outputDir, `${inputBaseName}.${options.outputFormat}`);
      
      // If the expected file exists but not at our desired path, move it
      if (fs.existsSync(expectedOutputPath) && expectedOutputPath !== options.outputPath) {
        fs.renameSync(expectedOutputPath, options.outputPath);
      }
      
      // Verify output file was created
      if (!fs.existsSync(options.outputPath)) {
        throw new Error(`Conversion failed: output file not created at ${options.outputPath}`);
      }
      
      logger.info('LibreOffice conversion completed successfully');
      
    } catch (error) {
      logger.error('LibreOffice conversion failed:', error);
      throw new Error(`Document conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert PDF to Word document
   */
  static async pdfToWord(file: Express.Multer.File): Promise<ProcessedFile> {
    try {
      logger.info('Starting PDF to Word conversion with LibreOffice');
      
      const outputName = `converted_${path.basename(file.originalname, '.pdf')}.docx`;
      const outputPath = path.join(config.paths.uploads, outputName);
      
      const isAvailable = await this.isLibreOfficeAvailable();
      if (!isAvailable) {
        throw new Error('LibreOffice is not available for document conversion');
      }
      
      await this.convertDocument({
        inputPath: file.path,
        outputPath,
        outputFormat: 'docx'
      });
      
      const stats = fs.statSync(outputPath);
      
      // Clean up original file
      await FileUtils.deleteFile(file.path);
      
      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: stats.size,
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        createdAt: new Date()
      };
      
      logger.info('PDF to Word conversion completed');
      return result;
      
    } catch (error) {
      logger.error('PDF to Word conversion error:', error);
      throw new Error(`Failed to convert PDF to Word: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert Word to PDF
   */
  static async wordToPdf(file: Express.Multer.File): Promise<ProcessedFile> {
    try {
      logger.info('Starting Word to PDF conversion with LibreOffice');
      
      const outputName = `converted_${path.basename(file.originalname, path.extname(file.originalname))}.pdf`;
      const outputPath = path.join(config.paths.uploads, outputName);
      
      const isAvailable = await this.isLibreOfficeAvailable();
      if (!isAvailable) {
        throw new Error('LibreOffice is not available for document conversion');
      }
      
      await this.convertDocument({
        inputPath: file.path,
        outputPath,
        outputFormat: 'pdf'
      });
      
      const stats = fs.statSync(outputPath);
      
      // Clean up original file
      await FileUtils.deleteFile(file.path);
      
      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: stats.size,
        mimeType: 'application/pdf',
        createdAt: new Date()
      };
      
      logger.info('Word to PDF conversion completed');
      return result;
      
    } catch (error) {
      logger.error('Word to PDF conversion error:', error);
      throw new Error(`Failed to convert Word to PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert Excel to PDF
   */
  static async excelToPdf(file: Express.Multer.File): Promise<ProcessedFile> {
    try {
      logger.info('Starting Excel to PDF conversion with LibreOffice');
      
      const outputName = `converted_${path.basename(file.originalname, path.extname(file.originalname))}.pdf`;
      const outputPath = path.join(config.paths.uploads, outputName);
      
      const isAvailable = await this.isLibreOfficeAvailable();
      if (!isAvailable) {
        throw new Error('LibreOffice is not available for document conversion');
      }
      
      await this.convertDocument({
        inputPath: file.path,
        outputPath,
        outputFormat: 'pdf'
      });
      
      const stats = fs.statSync(outputPath);
      
      // Clean up original file
      await FileUtils.deleteFile(file.path);
      
      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: stats.size,
        mimeType: 'application/pdf',
        createdAt: new Date()
      };
      
      logger.info('Excel to PDF conversion completed');
      return result;
      
    } catch (error) {
      logger.error('Excel to PDF conversion error:', error);
      throw new Error(`Failed to convert Excel to PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert PowerPoint to PDF
   */
  static async powerPointToPdf(file: Express.Multer.File): Promise<ProcessedFile> {
    try {
      logger.info('Starting PowerPoint to PDF conversion with LibreOffice');
      
      const outputName = `converted_${path.basename(file.originalname, path.extname(file.originalname))}.pdf`;
      const outputPath = path.join(config.paths.uploads, outputName);
      
      const isAvailable = await this.isLibreOfficeAvailable();
      if (!isAvailable) {
        throw new Error('LibreOffice is not available for document conversion');
      }
      
      await this.convertDocument({
        inputPath: file.path,
        outputPath,
        outputFormat: 'pdf'
      });
      
      const stats = fs.statSync(outputPath);
      
      // Clean up original file
      await FileUtils.deleteFile(file.path);
      
      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: stats.size,
        mimeType: 'application/pdf',
        createdAt: new Date()
      };
      
      logger.info('PowerPoint to PDF conversion completed');
      return result;
      
    } catch (error) {
      logger.error('PowerPoint to PDF conversion error:', error);
      throw new Error(`Failed to convert PowerPoint to PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
