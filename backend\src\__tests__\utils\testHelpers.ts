import fs from 'fs';
import path from 'path';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';

export class TestHelpers {
  static readonly TEST_DIR = path.join(__dirname, '../../../test-temp');
  
  /**
   * Create a test PDF with specified number of pages
   */
  static async createTestPdf(pages: number = 1, content?: string): Promise<string> {
    const pdfDoc = await PDFDocument.create();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    
    for (let i = 0; i < pages; i++) {
      const page = pdfDoc.addPage([595, 842]); // A4 size
      
      // Add page content
      const pageContent = content || `Test Page ${i + 1}`;
      page.drawText(pageContent, {
        x: 50,
        y: 750,
        size: 20,
        font,
        color: rgb(0, 0, 0),
      });
      
      // Add page number
      page.drawText(`Page ${i + 1} of ${pages}`, {
        x: 50,
        y: 50,
        size: 12,
        font,
        color: rgb(0.5, 0.5, 0.5),
      });
    }
    
    const pdfBytes = await pdfDoc.save();
    const fileName = `test-${Date.now()}-${Math.random().toString(36).substring(7)}.pdf`;
    const filePath = path.join(this.TEST_DIR, fileName);
    
    // Ensure directory exists
    if (!fs.existsSync(this.TEST_DIR)) {
      fs.mkdirSync(this.TEST_DIR, { recursive: true });
    }
    
    fs.writeFileSync(filePath, pdfBytes);
    return filePath;
  }
  
  /**
   * Create a mock Express.Multer.File object
   */
  static createMockFile(filePath: string, originalName: string, mimetype: string = 'application/pdf'): Express.Multer.File {
    const stats = fs.statSync(filePath);
    
    return {
      fieldname: 'file',
      originalname: originalName,
      encoding: '7bit',
      mimetype,
      size: stats.size,
      destination: path.dirname(filePath),
      filename: path.basename(filePath),
      path: filePath,
      buffer: Buffer.from(''),
      stream: {} as any,
    };
  }
  
  /**
   * Create a test image file
   */
  static async createTestImage(width: number = 200, height: number = 200): Promise<string> {
    // This is a simple 1x1 pixel PNG in base64
    const pngBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    const buffer = Buffer.from(pngBase64, 'base64');
    
    const fileName = `test-image-${Date.now()}-${Math.random().toString(36).substring(7)}.png`;
    const filePath = path.join(this.TEST_DIR, fileName);
    
    // Ensure directory exists
    if (!fs.existsSync(this.TEST_DIR)) {
      fs.mkdirSync(this.TEST_DIR, { recursive: true });
    }
    
    fs.writeFileSync(filePath, buffer);
    return filePath;
  }
  
  /**
   * Create a test Word document (simple text file for testing)
   */
  static async createTestWordDoc(): Promise<string> {
    const content = `
      <html>
        <body>
          <h1>Test Word Document</h1>
          <p>This is a test document for conversion testing.</p>
          <p>It contains multiple paragraphs and some formatting.</p>
        </body>
      </html>
    `;
    
    const fileName = `test-doc-${Date.now()}-${Math.random().toString(36).substring(7)}.html`;
    const filePath = path.join(this.TEST_DIR, fileName);
    
    // Ensure directory exists
    if (!fs.existsSync(this.TEST_DIR)) {
      fs.mkdirSync(this.TEST_DIR, { recursive: true });
    }
    
    fs.writeFileSync(filePath, content);
    return filePath;
  }
  
  /**
   * Clean up test files
   */
  static cleanupTestFiles(): void {
    if (fs.existsSync(this.TEST_DIR)) {
      const files = fs.readdirSync(this.TEST_DIR);
      files.forEach(file => {
        const filePath = path.join(this.TEST_DIR, file);
        try {
          fs.unlinkSync(filePath);
        } catch (error) {
          console.warn(`Failed to delete test file: ${filePath}`, error);
        }
      });
    }
  }
  
  /**
   * Verify PDF file is valid
   */
  static async verifyPdfFile(filePath: string): Promise<boolean> {
    try {
      const pdfBytes = fs.readFileSync(filePath);
      const pdf = await PDFDocument.load(pdfBytes);
      return pdf.getPageCount() > 0;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Get PDF page count
   */
  static async getPdfPageCount(filePath: string): Promise<number> {
    try {
      const pdfBytes = fs.readFileSync(filePath);
      const pdf = await PDFDocument.load(pdfBytes);
      return pdf.getPageCount();
    } catch (error) {
      return 0;
    }
  }
  
  /**
   * Create multiple test files for batch operations
   */
  static async createMultipleTestPdfs(count: number, pagesPerPdf: number = 1): Promise<string[]> {
    const files: string[] = [];
    
    for (let i = 0; i < count; i++) {
      const filePath = await this.createTestPdf(pagesPerPdf, `Test Document ${i + 1}`);
      files.push(filePath);
    }
    
    return files;
  }
  
  /**
   * Wait for a specified amount of time (useful for async operations)
   */
  static async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * Generate random string for test data
   */
  static generateRandomString(length: number = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}
