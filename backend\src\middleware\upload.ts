import multer from 'multer';
import path from 'path';
import { Request } from 'express';
import config from '../config';
import { FileUtils } from '../utils/fileUtils';
import logger from '../utils/logger';

// Ensure upload directories exist
FileUtils.ensureDirectoryExists(config.paths.uploads);
FileUtils.ensureDirectoryExists(config.paths.temp);

const storage = multer.diskStorage({
  destination: (req: Request, file: Express.Multer.File, cb) => {
    cb(null, config.paths.temp);
  },
  filename: (req: Request, file: Express.Multer.File, cb) => {
    const uniqueName = FileUtils.generateUniqueFilename(file.originalname);
    cb(null, uniqueName);
  }
});

const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Log file upload attempt
  logger.info(`File upload attempt: ${file.originalname}, mimetype: ${file.mimetype}`);
  
  // Check file type based on the endpoint
  const endpoint = req.path;
  
  if (endpoint.includes('/convert/from-')) {
    // For conversion from other formats to PDF
    if (FileUtils.isValidDocumentFile(file) || FileUtils.isValidImageFile(file)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only documents and images are allowed for conversion.'));
    }
  } else {
    // For PDF operations
    if (FileUtils.isValidPdfFile(file)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only PDF files are allowed.'));
    }
  }
};

export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 10, // Maximum 10 files per request
  },
});

// Middleware for single file upload
export const uploadSingle = upload.single('file');

// Middleware for multiple file upload
export const uploadMultiple = upload.array('files', 10);

// Middleware for fields with different file types
export const uploadFields = upload.fields([
  { name: 'files', maxCount: 10 },
  { name: 'watermarkImage', maxCount: 1 }
]);

// Error handling middleware for multer
export const handleUploadError = (error: any, req: Request, res: any, next: any) => {
  if (error instanceof multer.MulterError) {
    logger.error('Multer error:', error);
    
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        return res.status(400).json({
          success: false,
          error: `File too large. Maximum size is ${FileUtils.formatFileSize(config.upload.maxFileSize)}`
        });
      case 'LIMIT_FILE_COUNT':
        return res.status(400).json({
          success: false,
          error: 'Too many files. Maximum 10 files allowed.'
        });
      case 'LIMIT_UNEXPECTED_FILE':
        return res.status(400).json({
          success: false,
          error: 'Unexpected file field.'
        });
      default:
        return res.status(400).json({
          success: false,
          error: 'File upload error.'
        });
    }
  }
  
  if (error.message) {
    logger.error('Upload error:', error.message);
    return res.status(400).json({
      success: false,
      error: error.message
    });
  }
  
  next(error);
};
