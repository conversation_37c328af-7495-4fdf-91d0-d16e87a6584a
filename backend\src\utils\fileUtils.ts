import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import config from '../config';
import logger from './logger';

export class FileUtils {
  static ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      logger.info(`Created directory: ${dirPath}`);
    }
  }

  static generateUniqueFilename(originalName: string): string {
    const ext = path.extname(originalName);
    const name = path.basename(originalName, ext);
    const timestamp = Date.now();
    const uuid = uuidv4().substring(0, 8);
    return `${name}_${timestamp}_${uuid}${ext}`;
  }

  static getFileExtension(filename: string): string {
    return path.extname(filename).toLowerCase();
  }

  static isValidPdfFile(file: Express.Multer.File): boolean {
    const validMimeTypes = ['application/pdf'];
    const validExtensions = ['.pdf'];
    
    return validMimeTypes.includes(file.mimetype) && 
           validExtensions.includes(this.getFileExtension(file.originalname));
  }

  static isValidImageFile(file: Express.Multer.File): boolean {
    const validMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    const validExtensions = ['.jpg', '.jpeg', '.png', '.gif'];
    
    return validMimeTypes.includes(file.mimetype) && 
           validExtensions.includes(this.getFileExtension(file.originalname));
  }

  static isValidDocumentFile(file: Express.Multer.File): boolean {
    const validMimeTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/msword', // .doc
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
      'application/vnd.ms-powerpoint', // .ppt
    ];
    
    const validExtensions = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'];
    
    return validMimeTypes.includes(file.mimetype) && 
           validExtensions.includes(this.getFileExtension(file.originalname));
  }

  static async deleteFile(filePath: string): Promise<void> {
    try {
      if (fs.existsSync(filePath)) {
        await fs.promises.unlink(filePath);
        logger.info(`Deleted file: ${filePath}`);
      }
    } catch (error) {
      logger.error(`Error deleting file ${filePath}:`, error);
    }
  }

  static async cleanupOldFiles(): Promise<void> {
    const directories = [config.paths.uploads, config.paths.temp];
    const maxAge = config.cleanup.fileRetentionMs;
    const now = Date.now();

    for (const dir of directories) {
      try {
        if (!fs.existsSync(dir)) continue;

        const files = await fs.promises.readdir(dir);
        
        for (const file of files) {
          const filePath = path.join(dir, file);
          const stats = await fs.promises.stat(filePath);
          
          if (now - stats.mtime.getTime() > maxAge) {
            await this.deleteFile(filePath);
          }
        }
      } catch (error) {
        logger.error(`Error cleaning up directory ${dir}:`, error);
      }
    }
  }

  static getFileSizeInMB(sizeInBytes: number): number {
    return sizeInBytes / (1024 * 1024);
  }

  static formatFileSize(sizeInBytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = sizeInBytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }
}
