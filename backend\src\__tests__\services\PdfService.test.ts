import fs from 'fs';
import path from 'path';
import { PDFDocument } from 'pdf-lib';
import { PdfService } from '../../services/PdfService';

describe('PdfService', () => {
  const testDir = path.join(__dirname, '../../../test-temp');
  
  beforeEach(() => {
    // Ensure test directory exists
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
  });

  afterEach(() => {
    // Clean up test files
    if (fs.existsSync(testDir)) {
      const files = fs.readdirSync(testDir);
      files.forEach(file => {
        fs.unlinkSync(path.join(testDir, file));
      });
    }
  });

  const createTestPdf = async (pages: number = 1): Promise<string> => {
    const pdfDoc = await PDFDocument.create();
    
    for (let i = 0; i < pages; i++) {
      const page = pdfDoc.addPage([595, 842]);
      page.drawText(`Test Page ${i + 1}`, {
        x: 50,
        y: 750,
        size: 20,
      });
    }
    
    const pdfBytes = await pdfDoc.save();
    const filePath = path.join(testDir, `test-${Date.now()}-${Math.random()}.pdf`);
    fs.writeFileSync(filePath, pdfBytes);
    
    return filePath;
  };

  const createMockFile = (filePath: string, originalName: string): Express.Multer.File => ({
    fieldname: 'file',
    originalname: originalName,
    encoding: '7bit',
    mimetype: 'application/pdf',
    size: fs.statSync(filePath).size,
    destination: testDir,
    filename: path.basename(filePath),
    path: filePath,
    buffer: Buffer.from(''),
    stream: {} as any,
  });

  describe('mergePdfs', () => {
    it('should merge multiple PDF files successfully', async () => {
      // Create test PDFs
      const pdf1Path = await createTestPdf(2);
      const pdf2Path = await createTestPdf(3);
      
      const files = [
        createMockFile(pdf1Path, 'test1.pdf'),
        createMockFile(pdf2Path, 'test2.pdf'),
      ];

      const result = await PdfService.mergePdfs({ files });

      expect(result).toBeDefined();
      expect(result.filename).toContain('merged_');
      expect(result.size).toBeGreaterThan(0);
      expect(fs.existsSync(result.path)).toBe(true);

      // Verify merged PDF has correct number of pages
      const mergedPdfBytes = fs.readFileSync(result.path);
      const mergedPdf = await PDFDocument.load(mergedPdfBytes);
      expect(mergedPdf.getPageCount()).toBe(5); // 2 + 3 pages
    });

    it('should throw error when no files provided', async () => {
      await expect(PdfService.mergePdfs({ files: [] }))
        .rejects.toThrow('Failed to merge PDF files');
    });
  });

  describe('splitPdf', () => {
    it('should split PDF into individual pages', async () => {
      const pdfPath = await createTestPdf(3);
      const file = createMockFile(pdfPath, 'test.pdf');

      const results = await PdfService.splitPdf({
        file,
        splitType: 'all',
      });

      expect(results).toHaveLength(3);
      results.forEach((result, index) => {
        expect(result.filename).toContain(`page_${index + 1}`);
        expect(fs.existsSync(result.path)).toBe(true);
      });
    });

    it('should split PDF by specific pages', async () => {
      const pdfPath = await createTestPdf(5);
      const file = createMockFile(pdfPath, 'test.pdf');

      const results = await PdfService.splitPdf({
        file,
        splitType: 'specific',
        specificPages: '1, 3, 5',
      });

      expect(results).toHaveLength(3);
      expect(results[0].filename).toContain('page_1');
      expect(results[1].filename).toContain('page_3');
      expect(results[2].filename).toContain('page_5');
    });
  });

  describe('compressPdf', () => {
    it('should compress PDF file', async () => {
      const pdfPath = await createTestPdf(2);
      const file = createMockFile(pdfPath, 'test.pdf');
      const originalSize = file.size;

      const result = await PdfService.compressPdf({
        file,
        compressionLevel: 'medium',
      });

      expect(result).toBeDefined();
      expect(result.filename).toContain('compressed_');
      expect(fs.existsSync(result.path)).toBe(true);
      // Note: Compression might not always reduce size for small test PDFs
      expect(result.size).toBeGreaterThan(0);
    });
  });

  describe('rotatePdf', () => {
    it('should rotate PDF pages', async () => {
      const pdfPath = await createTestPdf(2);
      const file = createMockFile(pdfPath, 'test.pdf');

      const result = await PdfService.rotatePdf(file, 90);

      expect(result).toBeDefined();
      expect(result.filename).toContain('rotated_');
      expect(fs.existsSync(result.path)).toBe(true);

      // Verify rotation was applied
      const rotatedPdfBytes = fs.readFileSync(result.path);
      const rotatedPdf = await PDFDocument.load(rotatedPdfBytes);
      const page = rotatedPdf.getPage(0);
      expect(page.getRotation().angle).toBe(90);
    });
  });
});
