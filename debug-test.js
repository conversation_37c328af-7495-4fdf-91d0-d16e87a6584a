// Simple test to debug the PDF merge API
const fs = require('fs');
const FormData = require('form-data');
const fetch = require('node-fetch');

async function testPdfMerge() {
  try {
    // Create a simple test PDF using pdf-lib
    const { PDFDocument, StandardFonts, rgb } = require('pdf-lib');
    
    // Create test PDF 1
    const pdfDoc1 = await PDFDocument.create();
    const page1 = pdfDoc1.addPage([595, 842]);
    const font = await pdfDoc1.embedFont(StandardFonts.Helvetica);
    page1.drawText('Test PDF 1', { x: 50, y: 750, size: 20, font, color: rgb(0, 0, 0) });
    const pdf1Bytes = await pdfDoc1.save();
    
    // Create test PDF 2
    const pdfDoc2 = await PDFDocument.create();
    const page2 = pdfDoc2.addPage([595, 842]);
    page2.drawText('Test PDF 2', { x: 50, y: 750, size: 20, font, color: rgb(0, 0, 0) });
    const pdf2Bytes = await pdfDoc2.save();
    
    // Create form data
    const formData = new FormData();
    formData.append('files', Buffer.from(pdf1Bytes), 'test1.pdf');
    formData.append('files', Buffer.from(pdf2Bytes), 'test2.pdf');
    formData.append('outputName', 'merged_test.pdf');
    
    console.log('Sending request to merge PDFs...');
    
    // Send request
    const response = await fetch('http://localhost:3001/api/pdf/merge', {
      method: 'POST',
      body: formData,
    });
    
    const result = await response.json();
    console.log('Response status:', response.status);
    console.log('Response body:', JSON.stringify(result, null, 2));
    
    if (result.success && result.data && result.data.downloadUrl) {
      console.log('✅ Merge successful! Download URL:', result.data.downloadUrl);
      
      // Test download
      const downloadResponse = await fetch(`http://localhost:3001${result.data.downloadUrl}`);
      console.log('Download response status:', downloadResponse.status);
      
      if (downloadResponse.ok) {
        console.log('✅ Download successful!');
      } else {
        console.log('❌ Download failed');
      }
    } else {
      console.log('❌ Merge failed');
    }
    
  } catch (error) {
    console.error('Test error:', error);
  }
}

testPdfMerge();
