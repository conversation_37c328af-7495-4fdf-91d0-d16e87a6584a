import { Router } from 'express';
import { PdfController } from '../controllers/PdfController';
import { uploadSingle, uploadMultiple } from '../middleware/upload';
import { validateRequest, validateFiles, schemas } from '../middleware/validation';

const router = Router();

// Core PDF operations
router.post('/merge', 
  uploadMultiple,
  validateFiles,
  PdfController.mergePdfs
);

router.post('/split',
  uploadSingle,
  validateFiles,
  validateRequest(schemas.pdfSplit),
  PdfController.splitPdf
);

router.post('/compress',
  uploadSingle,
  validateFiles,
  validateRequest(schemas.pdfCompress),
  PdfController.compressPdf
);

router.post('/rotate',
  uploadSingle,
  validateFiles,
  validateRequest(schemas.pdfRotate),
  PdfController.rotatePdf
);

// PDF to other formats
router.post('/convert/to-images',
  uploadSingle,
  validateFiles,
  validateRequest(schemas.pdfConvert),
  PdfController.pdfToImages
);

router.post('/convert/to-word',
  uploadSingle,
  validateFiles,
  PdfController.pdfToWord
);

router.post('/convert/to-excel',
  uploadSingle,
  validateFiles,
  PdfController.pdfToExcel
);

router.post('/convert/to-powerpoint',
  uploadSingle,
  validateFiles,
  PdfController.pdfToPowerPoint
);

// Other formats to PDF
router.post('/convert/from-word',
  uploadSingle,
  validateFiles,
  PdfController.wordToPdf
);

router.post('/convert/from-excel',
  uploadSingle,
  validateFiles,
  PdfController.excelToPdf
);

router.post('/convert/from-powerpoint',
  uploadSingle,
  validateFiles,
  PdfController.powerPointToPdf
);

router.post('/convert/from-images',
  uploadMultiple,
  validateFiles,
  PdfController.imagesToPdf
);

export default router;
