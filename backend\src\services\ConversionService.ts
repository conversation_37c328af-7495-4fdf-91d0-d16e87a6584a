import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import { fromPath } from 'pdf2pic';
import config from '../config';
import logger from '../utils/logger';
import { FileUtils } from '../utils/fileUtils';
import { LibreOfficeService } from './LibreOfficeService';
import { PdfConversionOptions, ProcessedFile } from '../types';

export class ConversionService {
  
  /**
   * Convert PDF to images (JPG/PNG)
   */
  static async pdfToImage(options: PdfConversionOptions): Promise<ProcessedFile[]> {
    try {
      logger.info(`Starting PDF to image conversion: ${options.outputFormat}`);

      const results: ProcessedFile[] = [];

      // Configure pdf2pic options
      const convert = fromPath(options.file.path, {
        density: options.quality === 'high' ? 300 : options.quality === 'medium' ? 200 : 150,
        saveFilename: "page",
        savePath: config.paths.uploads,
        format: options.outputFormat === 'jpg' ? 'jpeg' : 'png',
        width: options.quality === 'high' ? 2480 : options.quality === 'medium' ? 1654 : 1240,
        height: options.quality === 'high' ? 3508 : options.quality === 'medium' ? 2339 : 1754
      });

      try {
        // Convert all pages
        const convertResults = await convert.bulk(-1, { responseType: "image" });

        for (const result of convertResults) {
          if (result.path) {
            const filename = path.basename(result.path);
            const outputName = `converted_${Date.now()}_${filename}`;
            const outputPath = path.join(config.paths.uploads, outputName);

            // Rename file to avoid conflicts
            fs.renameSync(result.path, outputPath);

            const stats = fs.statSync(outputPath);

            results.push({
              id: uuidv4(),
              originalName: outputName,
              filename: outputName,
              path: outputPath,
              size: stats.size,
              mimeType: `image/${options.outputFormat === 'jpg' ? 'jpeg' : 'png'}`,
              createdAt: new Date()
            });
          }
        }
      } catch (pdf2picError) {
        // Fallback to placeholder implementation if pdf2pic fails
        logger.warn('pdf2pic conversion failed, using fallback method:', pdf2picError);

        const pdfBytes = fs.readFileSync(options.file.path);
        const pdf = await PDFDocument.load(pdfBytes);
        const pageCount = pdf.getPageCount();

        for (let i = 0; i < pageCount; i++) {
          // Create a placeholder image using sharp
          const width = 595; // A4 width in points
          const height = 842; // A4 height in points

          const imageBuffer = await sharp({
            create: {
              width,
              height,
              channels: 3,
              background: { r: 255, g: 255, b: 255 }
            }
          })
          .jpeg({ quality: options.quality === 'high' ? 95 : options.quality === 'medium' ? 75 : 50 })
          .toBuffer();

          const outputName = `page_${i + 1}_${Date.now()}.${options.outputFormat}`;
          const outputPath = path.join(config.paths.uploads, outputName);

          fs.writeFileSync(outputPath, imageBuffer);

          results.push({
            id: uuidv4(),
            originalName: outputName,
            filename: outputName,
            path: outputPath,
            size: imageBuffer.length,
            mimeType: `image/${options.outputFormat}`,
            createdAt: new Date()
          });
        }
      }

      // Clean up original file
      await FileUtils.deleteFile(options.file.path);

      logger.info(`PDF to image conversion completed: ${results.length} images created`);
      return results;

    } catch (error) {
      logger.error('PDF to image conversion error:', error);
      throw new Error('Failed to convert PDF to images');
    }
  }
  
  /**
   * Convert PDF to Word document
   */
  static async pdfToWord(file: Express.Multer.File): Promise<ProcessedFile> {
    return LibreOfficeService.pdfToWord(file);
  }
  
  /**
   * Convert Word document to PDF
   */
  static async wordToPdf(file: Express.Multer.File): Promise<ProcessedFile> {
    return LibreOfficeService.wordToPdf(file);
  }
  
  /**
   * Convert Excel to PDF
   */
  static async excelToPdf(file: Express.Multer.File): Promise<ProcessedFile> {
    return LibreOfficeService.excelToPdf(file);
  }
  
  /**
   * Convert PDF to Excel
   */
  static async pdfToExcel(file: Express.Multer.File): Promise<ProcessedFile> {
    try {
      logger.info('Starting PDF to Excel conversion');

      // This is a placeholder implementation
      // In production, you'd use a proper PDF to Excel conversion library
      const outputName = `converted_${path.basename(file.originalname, '.pdf')}.xlsx`;
      const outputPath = path.join(config.paths.uploads, outputName);

      // Create a simple Excel workbook with placeholder content
      const workbook = XLSX.utils.book_new();

      // Create worksheet with extracted data placeholder
      const worksheetData = [
        ['Extracted from PDF', file.originalname],
        ['Page', 'Content'],
        ['1', 'This is placeholder content extracted from the PDF'],
        ['2', 'In production, this would contain actual PDF text and data'],
        ['3', 'Tables and structured data would be preserved'],
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

      // Write Excel file
      XLSX.writeFile(workbook, outputPath);

      // Clean up original file
      await FileUtils.deleteFile(file.path);

      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: fs.statSync(outputPath).size,
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        createdAt: new Date()
      };

      logger.info('PDF to Excel conversion completed');
      return result;

    } catch (error) {
      logger.error('PDF to Excel conversion error:', error);
      throw new Error('Failed to convert PDF to Excel');
    }
  }

  /**
   * Convert PDF to PowerPoint
   */
  static async pdfToPowerPoint(file: Express.Multer.File): Promise<ProcessedFile> {
    try {
      logger.info('Starting PDF to PowerPoint conversion');

      // This is a placeholder implementation
      // In production, you'd use a proper PDF to PowerPoint conversion library
      const outputName = `converted_${path.basename(file.originalname, '.pdf')}.pptx`;
      const outputPath = path.join(config.paths.uploads, outputName);

      // Create a simple PowerPoint-like content (as HTML for now)
      const pptContent = `
        <html>
          <head>
            <title>Converted from PDF</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 40px; }
              .slide { page-break-after: always; margin-bottom: 50px; }
              h1 { color: #1f4e79; font-size: 32px; }
              h2 { color: #2e75b6; font-size: 24px; }
              p { font-size: 16px; line-height: 1.5; }
            </style>
          </head>
          <body>
            <div class="slide">
              <h1>Slide 1: Converted from PDF</h1>
              <p>Original file: ${file.originalname}</p>
              <p>This presentation was converted from a PDF document.</p>
            </div>
            <div class="slide">
              <h2>Slide 2: Content Overview</h2>
              <p>In a production environment, this would contain:</p>
              <ul>
                <li>Extracted text from PDF pages</li>
                <li>Images and graphics</li>
                <li>Formatted content with proper layout</li>
                <li>Multiple slides based on PDF structure</li>
              </ul>
            </div>
          </body>
        </html>
      `;

      // Write HTML file (placeholder for PowerPoint)
      fs.writeFileSync(outputPath, pptContent);

      // Clean up original file
      await FileUtils.deleteFile(file.path);

      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: Buffer.byteLength(pptContent),
        mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        createdAt: new Date()
      };

      logger.info('PDF to PowerPoint conversion completed');
      return result;

    } catch (error) {
      logger.error('PDF to PowerPoint conversion error:', error);
      throw new Error('Failed to convert PDF to PowerPoint');
    }
  }

  /**
   * Convert PowerPoint to PDF
   */
  static async powerPointToPdf(file: Express.Multer.File): Promise<ProcessedFile> {
    return LibreOfficeService.powerPointToPdf(file);
  }

  /**
   * Convert images to PDF
   */
  static async imagesToPdf(files: Express.Multer.File[]): Promise<ProcessedFile> {
    try {
      logger.info(`Starting images to PDF conversion: ${files.length} images`);
      
      const pdfDoc = await PDFDocument.create();
      
      for (const file of files) {
        const imageBytes = fs.readFileSync(file.path);
        
        let image;
        if (file.mimetype === 'image/jpeg' || file.mimetype === 'image/jpg') {
          image = await pdfDoc.embedJpg(imageBytes);
        } else if (file.mimetype === 'image/png') {
          image = await pdfDoc.embedPng(imageBytes);
        } else {
          // Convert other formats to JPEG using sharp
          const jpegBytes = await sharp(imageBytes).jpeg().toBuffer();
          image = await pdfDoc.embedJpg(jpegBytes);
        }
        
        const page = pdfDoc.addPage();
        const { width, height } = page.getSize();
        
        // Scale image to fit page while maintaining aspect ratio
        const imageAspectRatio = image.width / image.height;
        const pageAspectRatio = width / height;
        
        let imageWidth, imageHeight;
        if (imageAspectRatio > pageAspectRatio) {
          imageWidth = width - 100; // 50px margin on each side
          imageHeight = imageWidth / imageAspectRatio;
        } else {
          imageHeight = height - 100; // 50px margin on top and bottom
          imageWidth = imageHeight * imageAspectRatio;
        }
        
        page.drawImage(image, {
          x: (width - imageWidth) / 2,
          y: (height - imageHeight) / 2,
          width: imageWidth,
          height: imageHeight,
        });
        
        // Clean up temporary file
        await FileUtils.deleteFile(file.path);
      }
      
      const outputName = `images_to_pdf_${Date.now()}.pdf`;
      const outputPath = path.join(config.paths.uploads, outputName);
      
      const pdfBytes = await pdfDoc.save();
      fs.writeFileSync(outputPath, pdfBytes);
      
      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: pdfBytes.length,
        mimeType: 'application/pdf',
        createdAt: new Date()
      };
      
      logger.info('Images to PDF conversion completed');
      return result;
      
    } catch (error) {
      logger.error('Images to PDF conversion error:', error);
      throw new Error('Failed to convert images to PDF');
    }
  }
}
