@echo off
REM PDF Toolkit Development Startup Script for Windows
REM This script starts both the backend and frontend development servers

echo 🚀 Starting PDF Toolkit Development Environment...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18.0 or higher.
    pause
    exit /b 1
)

echo ✅ Node.js version:
node --version

REM Check if ports are available
netstat -an | find "3001" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo ❌ Port 3001 is already in use. Please stop the service using this port.
    pause
    exit /b 1
)

netstat -an | find "5173" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo ❌ Port 5173 is already in use. Please stop the service using this port.
    pause
    exit /b 1
)

REM Setup backend
echo 📦 Setting up backend...
cd backend

REM Check if node_modules exists
if not exist "node_modules" (
    echo 📥 Installing backend dependencies...
    call npm install
)

REM Check if .env exists
if not exist ".env" (
    echo ⚙️ Creating backend environment configuration...
    copy .env.example .env
    echo ✅ Backend .env file created. You can modify it if needed.
)

REM Build backend
echo 🔨 Building backend...
call npm run build

REM Start backend in background
echo 🚀 Starting backend server on port 3001...
start "Backend Server" cmd /c "npm run dev"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Setup frontend
echo 📦 Setting up frontend...
cd ..

REM Check if node_modules exists
if not exist "node_modules" (
    echo 📥 Installing frontend dependencies...
    call npm install
)

REM Check if .env exists
if not exist ".env" (
    echo ⚙️ Creating frontend environment configuration...
    copy .env.example .env
    echo ✅ Frontend .env file created. You can modify it if needed.
)

echo.
echo 🎉 PDF Toolkit is starting up!
echo 📱 Frontend: http://localhost:5173
echo 🔧 Backend API: http://localhost:3001/api
echo 📊 Health Check: http://localhost:3001/api/health
echo.
echo Starting frontend server...

REM Start frontend
call npm run dev
