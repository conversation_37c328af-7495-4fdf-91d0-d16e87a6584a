import fs from 'fs';
import path from 'path';
import { ConversionService } from '../../services/ConversionService';
import { TestHelpers } from '../utils/testHelpers';

describe('ConversionService', () => {
  beforeEach(() => {
    TestHelpers.cleanupTestFiles();
  });

  afterAll(() => {
    TestHelpers.cleanupTestFiles();
  });

  describe('pdfToImage', () => {
    it('should convert PDF to images', async () => {
      const pdfPath = await TestHelpers.createTestPdf(2);
      const file = TestHelpers.createMockFile(pdfPath, 'test.pdf');

      const results = await ConversionService.pdfToImage({
        file,
        outputFormat: 'jpg',
        quality: 'medium'
      });

      expect(results).toHaveLength(2);
      results.forEach(result => {
        expect(result.filename).toContain('page_');
        expect(result.mimeType).toBe('image/jpg');
        expect(fs.existsSync(result.path)).toBe(true);
      });
    });

    it('should handle different image formats', async () => {
      const pdfPath = await TestHelpers.createTestPdf(1);
      const file = TestHelpers.createMockFile(pdfPath, 'test.pdf');

      const results = await ConversionService.pdfToImage({
        file,
        outputFormat: 'png',
        quality: 'high'
      });

      expect(results).toHaveLength(1);
      expect(results[0].mimeType).toBe('image/png');
    });
  });

  describe('pdfToWord', () => {
    it('should convert PDF to Word document', async () => {
      const pdfPath = await TestHelpers.createTestPdf(1, 'Test content for Word conversion');
      const file = TestHelpers.createMockFile(pdfPath, 'test.pdf');

      const result = await ConversionService.pdfToWord(file);

      expect(result).toBeDefined();
      expect(result.filename).toContain('converted_');
      expect(result.filename).toContain('.docx');
      expect(result.mimeType).toBe('application/vnd.openxmlformats-officedocument.wordprocessingml.document');
      expect(fs.existsSync(result.path)).toBe(true);
    });
  });

  describe('pdfToExcel', () => {
    it('should convert PDF to Excel spreadsheet', async () => {
      const pdfPath = await TestHelpers.createTestPdf(1, 'Test data for Excel conversion');
      const file = TestHelpers.createMockFile(pdfPath, 'test.pdf');

      const result = await ConversionService.pdfToExcel(file);

      expect(result).toBeDefined();
      expect(result.filename).toContain('converted_');
      expect(result.filename).toContain('.xlsx');
      expect(result.mimeType).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      expect(fs.existsSync(result.path)).toBe(true);
    });
  });

  describe('pdfToPowerPoint', () => {
    it('should convert PDF to PowerPoint presentation', async () => {
      const pdfPath = await TestHelpers.createTestPdf(1, 'Test content for PowerPoint conversion');
      const file = TestHelpers.createMockFile(pdfPath, 'test.pdf');

      const result = await ConversionService.pdfToPowerPoint(file);

      expect(result).toBeDefined();
      expect(result.filename).toContain('converted_');
      expect(result.filename).toContain('.pptx');
      expect(result.mimeType).toBe('application/vnd.openxmlformats-officedocument.presentationml.presentation');
      expect(fs.existsSync(result.path)).toBe(true);
    });
  });

  describe('wordToPdf', () => {
    it('should convert Word document to PDF', async () => {
      const docPath = await TestHelpers.createTestWordDoc();
      const file = TestHelpers.createMockFile(docPath, 'test.html', 'text/html');

      const result = await ConversionService.wordToPdf(file);

      expect(result).toBeDefined();
      expect(result.filename).toContain('converted_');
      expect(result.filename).toContain('.pdf');
      expect(result.mimeType).toBe('application/pdf');
      expect(fs.existsSync(result.path)).toBe(true);

      // Verify it's a valid PDF
      const isValidPdf = await TestHelpers.verifyPdfFile(result.path);
      expect(isValidPdf).toBe(true);
    });
  });

  describe('excelToPdf', () => {
    it('should convert Excel to PDF', async () => {
      // Create a simple CSV file to simulate Excel
      const csvContent = 'Name,Age,City\nJohn,30,Paris\nJane,25,London';
      const csvPath = path.join(TestHelpers.TEST_DIR, 'test.csv');
      fs.writeFileSync(csvPath, csvContent);
      
      const file = TestHelpers.createMockFile(csvPath, 'test.xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

      const result = await ConversionService.excelToPdf(file);

      expect(result).toBeDefined();
      expect(result.filename).toContain('converted_');
      expect(result.filename).toContain('.pdf');
      expect(result.mimeType).toBe('application/pdf');
      expect(fs.existsSync(result.path)).toBe(true);

      // Verify it's a valid PDF
      const isValidPdf = await TestHelpers.verifyPdfFile(result.path);
      expect(isValidPdf).toBe(true);
    });
  });

  describe('powerPointToPdf', () => {
    it('should convert PowerPoint to PDF', async () => {
      // Create a simple HTML file to simulate PowerPoint
      const pptContent = '<html><body><h1>Test Presentation</h1><p>Slide content</p></body></html>';
      const pptPath = path.join(TestHelpers.TEST_DIR, 'test.html');
      fs.writeFileSync(pptPath, pptContent);
      
      const file = TestHelpers.createMockFile(pptPath, 'test.pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation');

      const result = await ConversionService.powerPointToPdf(file);

      expect(result).toBeDefined();
      expect(result.filename).toContain('converted_');
      expect(result.filename).toContain('.pdf');
      expect(result.mimeType).toBe('application/pdf');
      expect(fs.existsSync(result.path)).toBe(true);

      // Verify it's a valid PDF
      const isValidPdf = await TestHelpers.verifyPdfFile(result.path);
      expect(isValidPdf).toBe(true);
    });
  });

  describe('imagesToPdf', () => {
    it('should convert multiple images to PDF', async () => {
      const image1Path = await TestHelpers.createTestImage();
      const image2Path = await TestHelpers.createTestImage();
      
      const files = [
        TestHelpers.createMockFile(image1Path, 'image1.png', 'image/png'),
        TestHelpers.createMockFile(image2Path, 'image2.png', 'image/png')
      ];

      const result = await ConversionService.imagesToPdf(files);

      expect(result).toBeDefined();
      expect(result.filename).toContain('images_to_pdf_');
      expect(result.filename).toContain('.pdf');
      expect(result.mimeType).toBe('application/pdf');
      expect(fs.existsSync(result.path)).toBe(true);

      // Verify it's a valid PDF with correct number of pages
      const isValidPdf = await TestHelpers.verifyPdfFile(result.path);
      expect(isValidPdf).toBe(true);
      
      const pageCount = await TestHelpers.getPdfPageCount(result.path);
      expect(pageCount).toBe(2);
    });

    it('should handle single image conversion', async () => {
      const imagePath = await TestHelpers.createTestImage();
      const files = [TestHelpers.createMockFile(imagePath, 'image.jpg', 'image/jpeg')];

      const result = await ConversionService.imagesToPdf(files);

      expect(result).toBeDefined();
      expect(fs.existsSync(result.path)).toBe(true);

      const pageCount = await TestHelpers.getPdfPageCount(result.path);
      expect(pageCount).toBe(1);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid PDF files gracefully', async () => {
      const invalidPath = path.join(TestHelpers.TEST_DIR, 'invalid.pdf');
      fs.writeFileSync(invalidPath, 'This is not a PDF file');
      
      const file = TestHelpers.createMockFile(invalidPath, 'invalid.pdf');

      await expect(ConversionService.pdfToWord(file))
        .rejects.toThrow('Failed to convert PDF to Word');
    });

    it('should handle missing files gracefully', async () => {
      const file = TestHelpers.createMockFile('/nonexistent/file.pdf', 'missing.pdf');

      await expect(ConversionService.pdfToExcel(file))
        .rejects.toThrow('Failed to convert PDF to Excel');
    });
  });
});
