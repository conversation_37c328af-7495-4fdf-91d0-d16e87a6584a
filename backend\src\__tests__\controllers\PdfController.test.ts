import request from 'supertest';
import express from 'express';
import fs from 'fs';
import path from 'path';
import { PDFDocument } from 'pdf-lib';
import { PdfController } from '../../controllers/PdfController';
import { uploadMultiple, uploadSingle } from '../../middleware/upload';

// Mock the services
jest.mock('../../services/PdfService');
jest.mock('../../services/ConversionService');

const app = express();
app.use(express.json());

// Test routes
app.post('/merge', uploadMultiple, PdfController.mergePdfs);
app.post('/split', uploadSingle, PdfController.splitPdf);
app.post('/compress', uploadSingle, PdfController.compressPdf);

describe('PdfController', () => {
  const testDir = path.join(__dirname, '../../../test-temp');
  
  beforeEach(() => {
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
  });

  const createTestPdf = async (): Promise<Buffer> => {
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([595, 842]);
    page.drawText('Test PDF Content', {
      x: 50,
      y: 750,
      size: 20,
    });
    return Buffer.from(await pdfDoc.save());
  };

  describe('POST /merge', () => {
    it('should return 400 when less than 2 files provided', async () => {
      const pdfBuffer = await createTestPdf();
      
      const response = await request(app)
        .post('/merge')
        .attach('files', pdfBuffer, 'test1.pdf')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('At least 2 PDF files are required');
    });

    it('should handle merge request with valid files', async () => {
      // Mock the PdfService.mergePdfs method
      const { PdfService } = require('../../services/PdfService');
      PdfService.mergePdfs = jest.fn().mockResolvedValue({
        filename: 'merged_test.pdf',
        originalName: 'merged_test.pdf',
        size: 1024,
        path: '/test/path/merged_test.pdf',
      });

      const pdfBuffer = await createTestPdf();
      
      const response = await request(app)
        .post('/merge')
        .attach('files', pdfBuffer, 'test1.pdf')
        .attach('files', pdfBuffer, 'test2.pdf')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.fileId).toBe('merged_test.pdf');
      expect(PdfService.mergePdfs).toHaveBeenCalled();
    });
  });

  describe('POST /split', () => {
    it('should return 400 when no file provided', async () => {
      const response = await request(app)
        .post('/split')
        .send({ splitType: 'all' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('PDF file is required');
    });

    it('should handle split request with valid file', async () => {
      // Mock the PdfService.splitPdf method
      const { PdfService } = require('../../services/PdfService');
      PdfService.splitPdf = jest.fn().mockResolvedValue([
        {
          filename: 'page_1_test.pdf',
          originalName: 'page_1_test.pdf',
          size: 512,
          path: '/test/path/page_1_test.pdf',
        },
        {
          filename: 'page_2_test.pdf',
          originalName: 'page_2_test.pdf',
          size: 512,
          path: '/test/path/page_2_test.pdf',
        },
      ]);

      const pdfBuffer = await createTestPdf();
      
      const response = await request(app)
        .post('/split')
        .attach('file', pdfBuffer, 'test.pdf')
        .field('splitType', 'all')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.files).toHaveLength(2);
      expect(PdfService.splitPdf).toHaveBeenCalled();
    });
  });

  describe('POST /compress', () => {
    it('should handle compress request with valid file', async () => {
      // Mock the PdfService.compressPdf method
      const { PdfService } = require('../../services/PdfService');
      PdfService.compressPdf = jest.fn().mockResolvedValue({
        filename: 'compressed_test.pdf',
        originalName: 'compressed_test.pdf',
        size: 800,
        path: '/test/path/compressed_test.pdf',
      });

      const pdfBuffer = await createTestPdf();
      
      const response = await request(app)
        .post('/compress')
        .attach('file', pdfBuffer, 'test.pdf')
        .field('compressionLevel', 'medium')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.fileId).toBe('compressed_test.pdf');
      expect(PdfService.compressPdf).toHaveBeenCalled();
    });
  });
});
