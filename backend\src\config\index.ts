import dotenv from 'dotenv';
import path from 'path';

dotenv.config();

export const config = {
  server: {
    port: parseInt(process.env.PORT || '3001', 10),
    nodeEnv: process.env.NODE_ENV || 'development',
  },
  
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '52428800', 10), // 50MB
    maxTotalSize: parseInt(process.env.MAX_TOTAL_SIZE || '209715200', 10), // 200MB
    uploadDir: process.env.UPLOAD_DIR || './uploads',
    tempDir: process.env.TEMP_DIR || './temp',
  },
  
  security: {
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
  },
  
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/app.log',
  },
  
  cors: {
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:5173',
  },
  
  cleanup: {
    intervalMs: parseInt(process.env.CLEANUP_INTERVAL_MS || '3600000', 10), // 1 hour
    fileRetentionMs: parseInt(process.env.FILE_RETENTION_MS || '3600000', 10), // 1 hour
  },
  
  paths: {
    uploads: path.resolve(process.env.UPLOAD_DIR || './uploads'),
    temp: path.resolve(process.env.TEMP_DIR || './temp'),
    logs: path.resolve('./logs'),
  },
};

export default config;
