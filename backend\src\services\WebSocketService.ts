import { Server as HttpServer } from 'http';
import { Server as SocketIOServer, Socket } from 'socket.io';
import logger from '../utils/logger';
import { progressTracker, ProgressUpdate } from './ProgressTracker';
import { MemoryManager } from './MemoryManager';

export class WebSocketService {
  private io: SocketIOServer;
  private connectedClients: Map<string, Socket> = new Map();

  constructor(httpServer: HttpServer) {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:5173",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.setupEventHandlers();
    this.setupProgressTracking();
    logger.info('WebSocket service initialized');
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    this.io.on('connection', (socket: Socket) => {
      logger.info(`Client connected: ${socket.id}`);
      this.connectedClients.set(socket.id, socket);

      // Send initial system status
      this.sendSystemStatus(socket);

      // Handle client events
      socket.on('subscribe-job', (jobId: string) => {
        socket.join(`job-${jobId}`);
        logger.debug(`Client ${socket.id} subscribed to job ${jobId}`);
        
        // Send current job status if available
        const job = progressTracker.getJob(jobId);
        if (job) {
          socket.emit('job-progress', {
            jobId,
            progress: job.progress,
            status: job.status,
            result: job.result,
            error: job.error
          });
        }
      });

      socket.on('unsubscribe-job', (jobId: string) => {
        socket.leave(`job-${jobId}`);
        logger.debug(`Client ${socket.id} unsubscribed from job ${jobId}`);
      });

      socket.on('get-job-status', (jobId: string) => {
        const job = progressTracker.getJob(jobId);
        socket.emit('job-status', job || { error: 'Job not found' });
      });

      socket.on('get-system-status', () => {
        this.sendSystemStatus(socket);
      });

      socket.on('disconnect', () => {
        logger.info(`Client disconnected: ${socket.id}`);
        this.connectedClients.delete(socket.id);
      });

      socket.on('error', (error) => {
        logger.error(`WebSocket error for client ${socket.id}:`, error);
      });
    });
  }

  /**
   * Setup progress tracking integration
   */
  private setupProgressTracking(): void {
    progressTracker.on('progress', (update: ProgressUpdate) => {
      // Broadcast to all clients subscribed to this job
      this.io.to(`job-${update.jobId}`).emit('job-progress', update);
      
      // Also broadcast to general progress listeners
      this.io.emit('progress-update', update);
    });
  }

  /**
   * Send system status to a specific client
   */
  private sendSystemStatus(socket: Socket): void {
    const memoryStatus = MemoryManager.checkMemoryLimits();
    const jobStats = progressTracker.getStats();
    
    const systemStatus = {
      memory: {
        status: memoryStatus.status,
        usage: memoryStatus.usage.formatted,
        message: memoryStatus.message
      },
      jobs: jobStats,
      timestamp: new Date().toISOString(),
      connectedClients: this.connectedClients.size
    };
    
    socket.emit('system-status', systemStatus);
  }

  /**
   * Broadcast system status to all connected clients
   */
  public broadcastSystemStatus(): void {
    const memoryStatus = MemoryManager.checkMemoryLimits();
    const jobStats = progressTracker.getStats();
    
    const systemStatus = {
      memory: {
        status: memoryStatus.status,
        usage: memoryStatus.usage.formatted,
        message: memoryStatus.message
      },
      jobs: jobStats,
      timestamp: new Date().toISOString(),
      connectedClients: this.connectedClients.size
    };
    
    this.io.emit('system-status', systemStatus);
  }

  /**
   * Send progress update for a specific job
   */
  public sendJobProgress(jobId: string, update: Partial<ProgressUpdate>): void {
    const fullUpdate: ProgressUpdate = {
      jobId,
      progress: 0,
      status: 'pending',
      ...update
    };
    
    this.io.to(`job-${jobId}`).emit('job-progress', fullUpdate);
  }

  /**
   * Send notification to all clients
   */
  public sendNotification(message: string, type: 'info' | 'warning' | 'error' = 'info'): void {
    this.io.emit('notification', {
      message,
      type,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Send error notification for a specific job
   */
  public sendJobError(jobId: string, error: string): void {
    this.io.to(`job-${jobId}`).emit('job-error', {
      jobId,
      error,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Get connected clients count
   */
  public getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  /**
   * Get all connected client IDs
   */
  public getConnectedClientIds(): string[] {
    return Array.from(this.connectedClients.keys());
  }

  /**
   * Disconnect all clients
   */
  public disconnectAllClients(): void {
    this.io.disconnectSockets();
    this.connectedClients.clear();
    logger.info('All WebSocket clients disconnected');
  }

  /**
   * Start periodic system status broadcasts
   */
  public startPeriodicStatusBroadcast(intervalMs: number = 30000): NodeJS.Timeout {
    return setInterval(() => {
      if (this.connectedClients.size > 0) {
        this.broadcastSystemStatus();
      }
    }, intervalMs);
  }

  /**
   * Send file processing started notification
   */
  public notifyFileProcessingStarted(jobId: string, fileName: string, operation: string): void {
    this.io.to(`job-${jobId}`).emit('processing-started', {
      jobId,
      fileName,
      operation,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Send file processing completed notification
   */
  public notifyFileProcessingCompleted(jobId: string, fileName: string, downloadUrl: string): void {
    this.io.to(`job-${jobId}`).emit('processing-completed', {
      jobId,
      fileName,
      downloadUrl,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Get WebSocket server instance
   */
  public getIO(): SocketIOServer {
    return this.io;
  }
}

// Global WebSocket service instance (will be initialized in server.ts)
export let webSocketService: WebSocketService;
