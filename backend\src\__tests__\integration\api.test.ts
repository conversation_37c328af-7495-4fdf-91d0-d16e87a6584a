import request from 'supertest';
import fs from 'fs';
import path from 'path';
import app from '../../server';
import { TestHelpers } from '../utils/testHelpers';

describe('API Integration Tests', () => {
  beforeEach(() => {
    // Clean up before each test
    TestHelpers.cleanupTestFiles();
  });

  afterAll(() => {
    // Clean up after all tests
    TestHelpers.cleanupTestFiles();
  });

  describe('Health Check', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('healthy');
      expect(response.body.data.version).toBeDefined();
    });
  });

  describe('PDF Operations', () => {
    describe('POST /api/pdf/merge', () => {
      it('should merge multiple PDF files', async () => {
        // Create test PDFs
        const pdf1Path = await TestHelpers.createTestPdf(2, 'First PDF');
        const pdf2Path = await TestHelpers.createTestPdf(3, 'Second PDF');

        const response = await request(app)
          .post('/api/pdf/merge')
          .attach('files', fs.readFileSync(pdf1Path), 'test1.pdf')
          .attach('files', fs.readFileSync(pdf2Path), 'test2.pdf')
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.fileId).toBeDefined();
        expect(response.body.data.downloadUrl).toContain('/api/download/');
        expect(response.body.message).toContain('merged successfully');
      });

      it('should return error when less than 2 files provided', async () => {
        const pdfPath = await TestHelpers.createTestPdf(1);

        const response = await request(app)
          .post('/api/pdf/merge')
          .attach('files', fs.readFileSync(pdfPath), 'test.pdf')
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error).toContain('At least 2 PDF files are required');
      });
    });

    describe('POST /api/pdf/split', () => {
      it('should split PDF into all pages', async () => {
        const pdfPath = await TestHelpers.createTestPdf(3);

        const response = await request(app)
          .post('/api/pdf/split')
          .attach('file', fs.readFileSync(pdfPath), 'test.pdf')
          .field('splitType', 'all')
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.files).toHaveLength(3);
        expect(response.body.message).toContain('split into 3 files');
      });

      it('should split PDF by specific pages', async () => {
        const pdfPath = await TestHelpers.createTestPdf(5);

        const response = await request(app)
          .post('/api/pdf/split')
          .attach('file', fs.readFileSync(pdfPath), 'test.pdf')
          .field('splitType', 'specific')
          .field('specificPages', '1, 3, 5')
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.files).toHaveLength(3);
      });

      it('should return error when no file provided', async () => {
        const response = await request(app)
          .post('/api/pdf/split')
          .field('splitType', 'all')
          .expect(400);

        expect(response.body.success).toBe(false);
        expect(response.body.error).toContain('PDF file is required');
      });
    });

    describe('POST /api/pdf/compress', () => {
      it('should compress PDF file', async () => {
        const pdfPath = await TestHelpers.createTestPdf(2);

        const response = await request(app)
          .post('/api/pdf/compress')
          .attach('file', fs.readFileSync(pdfPath), 'test.pdf')
          .field('compressionLevel', 'medium')
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.fileId).toBeDefined();
        expect(response.body.data.originalSize).toBeDefined();
        expect(response.body.data.compressedSize).toBeDefined();
        expect(response.body.message).toContain('compressed successfully');
      });
    });

    describe('POST /api/pdf/rotate', () => {
      it('should rotate PDF pages', async () => {
        const pdfPath = await TestHelpers.createTestPdf(2);

        const response = await request(app)
          .post('/api/pdf/rotate')
          .attach('file', fs.readFileSync(pdfPath), 'test.pdf')
          .field('rotation', '90')
          .field('pages', 'all')
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.fileId).toBeDefined();
        expect(response.body.message).toContain('rotated successfully');
      });
    });
  });

  describe('PDF Conversion', () => {
    describe('POST /api/pdf/convert/to-images', () => {
      it('should convert PDF to images', async () => {
        const pdfPath = await TestHelpers.createTestPdf(2);

        const response = await request(app)
          .post('/api/pdf/convert/to-images')
          .attach('file', fs.readFileSync(pdfPath), 'test.pdf')
          .field('outputFormat', 'jpg')
          .field('quality', 'medium')
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.files).toHaveLength(2);
        expect(response.body.message).toContain('converted to 2 images');
      });
    });

    describe('POST /api/pdf/convert/to-word', () => {
      it('should convert PDF to Word', async () => {
        const pdfPath = await TestHelpers.createTestPdf(1);

        const response = await request(app)
          .post('/api/pdf/convert/to-word')
          .attach('file', fs.readFileSync(pdfPath), 'test.pdf')
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.fileId).toBeDefined();
        expect(response.body.message).toContain('converted to Word');
      });
    });

    describe('POST /api/pdf/convert/from-word', () => {
      it('should convert Word to PDF', async () => {
        const docPath = await TestHelpers.createTestWordDoc();

        const response = await request(app)
          .post('/api/pdf/convert/from-word')
          .attach('file', fs.readFileSync(docPath), 'test.html')
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.fileId).toBeDefined();
        expect(response.body.message).toContain('converted to PDF');
      });
    });

    describe('POST /api/pdf/convert/from-images', () => {
      it('should convert images to PDF', async () => {
        const image1Path = await TestHelpers.createTestImage();
        const image2Path = await TestHelpers.createTestImage();

        const response = await request(app)
          .post('/api/pdf/convert/from-images')
          .attach('files', fs.readFileSync(image1Path), 'image1.png')
          .attach('files', fs.readFileSync(image2Path), 'image2.png')
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data.fileId).toBeDefined();
        expect(response.body.message).toContain('2 images converted to PDF');
      });
    });
  });

  describe('File Download', () => {
    it('should download processed file', async () => {
      // First, create and process a file
      const pdfPath = await TestHelpers.createTestPdf(1);
      
      const processResponse = await request(app)
        .post('/api/pdf/compress')
        .attach('file', fs.readFileSync(pdfPath), 'test.pdf')
        .field('compressionLevel', 'medium')
        .expect(200);

      const fileId = processResponse.body.data.fileId;

      // Then download it
      const downloadResponse = await request(app)
        .get(`/api/download/${fileId}`)
        .expect(200);

      expect(downloadResponse.headers['content-type']).toBe('application/octet-stream');
      expect(downloadResponse.headers['content-disposition']).toContain('attachment');
    });

    it('should return 404 for non-existent file', async () => {
      const response = await request(app)
        .get('/api/download/non-existent-file.pdf')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('File not found');
    });

    it('should return 400 for invalid file ID format', async () => {
      const response = await request(app)
        .get('/api/download/invalid-file-id')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid file ID format');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid file types', async () => {
      const textContent = 'This is not a PDF file';
      
      const response = await request(app)
        .post('/api/pdf/compress')
        .attach('file', Buffer.from(textContent), 'test.txt')
        .field('compressionLevel', 'medium')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid file type');
    });

    it('should handle missing required fields', async () => {
      const pdfPath = await TestHelpers.createTestPdf(1);

      const response = await request(app)
        .post('/api/pdf/split')
        .attach('file', fs.readFileSync(pdfPath), 'test.pdf')
        // Missing splitType field
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });
});
