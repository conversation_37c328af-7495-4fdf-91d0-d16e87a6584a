import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { exec } from 'child_process';
import { promisify } from 'util';
import { PDFDocument } from 'pdf-lib';
import config from '../config';
import logger from '../utils/logger';
import { FileUtils } from '../utils/fileUtils';
import { PdfCompressOptions, ProcessedFile, CompressionResult } from '../types';

const execAsync = promisify(exec);

export interface CompressionSettings {
  quality: 'screen' | 'ebook' | 'printer' | 'prepress';
  imageQuality: number; // 0-100
  colorImageResolution: number;
  grayscaleImageResolution: number;
  monoImageResolution: number;
  optimizeImages: boolean;
  removeMetadata: boolean;
  linearize: boolean;
}

export class CompressionService {
  
  /**
   * Get compression settings based on compression level
   */
  private static getCompressionSettings(level: 'low' | 'medium' | 'high'): CompressionSettings {
    switch (level) {
      case 'low':
        return {
          quality: 'printer',
          imageQuality: 85,
          colorImageResolution: 150,
          grayscaleImageResolution: 150,
          monoImageResolution: 300,
          optimizeImages: true,
          removeMetadata: false,
          linearize: false
        };
      case 'medium':
        return {
          quality: 'ebook',
          imageQuality: 70,
          colorImageResolution: 120,
          grayscaleImageResolution: 120,
          monoImageResolution: 200,
          optimizeImages: true,
          removeMetadata: true,
          linearize: true
        };
      case 'high':
        return {
          quality: 'screen',
          imageQuality: 50,
          colorImageResolution: 72,
          grayscaleImageResolution: 72,
          monoImageResolution: 150,
          optimizeImages: true,
          removeMetadata: true,
          linearize: true
        };
      default:
        return this.getCompressionSettings('medium');
    }
  }

  /**
   * Check if Ghostscript is available
   */
  private static async isGhostscriptAvailable(): Promise<boolean> {
    try {
      await execAsync('gs --version');
      return true;
    } catch (error) {
      logger.warn('Ghostscript not available, falling back to pdf-lib compression');
      return false;
    }
  }

  /**
   * Compress PDF using Ghostscript
   */
  private static async compressWithGhostscript(
    inputPath: string,
    outputPath: string,
    settings: CompressionSettings
  ): Promise<void> {
    const gsCommand = [
      'gs',
      '-sDEVICE=pdfwrite',
      '-dCompatibilityLevel=1.4',
      `-dPDFSETTINGS=/${settings.quality}`,
      '-dNOPAUSE',
      '-dQUIET',
      '-dBATCH',
      settings.linearize ? '-dFastWebView=true' : '',
      settings.removeMetadata ? '-dPrinted=false' : '',
      settings.optimizeImages ? '-dOptimize=true' : '',
      `-dColorImageResolution=${settings.colorImageResolution}`,
      `-dGrayImageResolution=${settings.grayscaleImageResolution}`,
      `-dMonoImageResolution=${settings.monoImageResolution}`,
      `-dColorImageDownsampleThreshold=1.0`,
      `-dGrayImageDownsampleThreshold=1.0`,
      `-dMonoImageDownsampleThreshold=1.0`,
      `-dJPEGQ=${settings.imageQuality}`,
      `-sOutputFile=${outputPath}`,
      inputPath
    ].filter(Boolean).join(' ');

    logger.info(`Executing Ghostscript compression: ${gsCommand}`);
    
    try {
      const { stdout, stderr } = await execAsync(gsCommand);
      if (stderr && !stderr.includes('Warning')) {
        logger.warn(`Ghostscript warnings: ${stderr}`);
      }
      logger.info('Ghostscript compression completed successfully');
    } catch (error) {
      logger.error('Ghostscript compression failed:', error);
      throw new Error(`Ghostscript compression failed: ${error}`);
    }
  }

  /**
   * Fallback compression using pdf-lib
   */
  private static async compressWithPdfLib(
    inputPath: string,
    outputPath: string,
    level: 'low' | 'medium' | 'high'
  ): Promise<void> {
    logger.info('Using pdf-lib fallback compression');
    
    const pdfBytes = fs.readFileSync(inputPath);
    const pdf = await PDFDocument.load(pdfBytes);
    
    const compressedBytes = await pdf.save({
      useObjectStreams: level !== 'low',
      addDefaultPage: false,
      objectsPerTick: level === 'high' ? 50 : 20
    });
    
    fs.writeFileSync(outputPath, compressedBytes);
    logger.info('pdf-lib compression completed');
  }

  /**
   * Analyze compression results
   */
  private static analyzeCompression(
    originalSize: number,
    compressedSize: number
  ): CompressionResult {
    const compressionRatio = ((originalSize - compressedSize) / originalSize * 100);
    const spaceSaved = originalSize - compressedSize;
    
    return {
      originalSize,
      compressedSize,
      compressionRatio: Math.max(0, compressionRatio), // Ensure non-negative
      spaceSaved: Math.max(0, spaceSaved),
      compressionRatioFormatted: `${Math.max(0, compressionRatio).toFixed(1)}%`,
      spaceSavedFormatted: FileUtils.formatFileSize(Math.max(0, spaceSaved))
    };
  }

  /**
   * Main compression method with enhanced capabilities
   */
  static async compressPdf(options: PdfCompressOptions): Promise<ProcessedFile & { compressionResult: CompressionResult }> {
    try {
      logger.info(`Starting enhanced PDF compression: ${options.compressionLevel}`);
      
      const originalSize = options.file.size;
      const settings = this.getCompressionSettings(options.compressionLevel);
      
      // Generate output filename
      const outputName = `compressed_${path.basename(options.file.originalname)}`;
      const outputPath = path.join(config.paths.uploads, outputName);
      
      // Try Ghostscript first, fallback to pdf-lib
      const useGhostscript = await this.isGhostscriptAvailable();
      
      if (useGhostscript) {
        await this.compressWithGhostscript(options.file.path, outputPath, settings);
      } else {
        await this.compressWithPdfLib(options.file.path, outputPath, options.compressionLevel);
      }
      
      // Verify output file exists and get size
      if (!fs.existsSync(outputPath)) {
        throw new Error('Compression failed: output file not created');
      }
      
      const compressedSize = fs.statSync(outputPath).size;
      
      // If compressed file is larger, use original
      if (compressedSize >= originalSize) {
        logger.warn('Compressed file is larger than original, using original file');
        fs.copyFileSync(options.file.path, outputPath);
      }
      
      const finalSize = fs.statSync(outputPath).size;
      const compressionResult = this.analyzeCompression(originalSize, finalSize);
      
      // Clean up original file
      await FileUtils.deleteFile(options.file.path);
      
      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: finalSize,
        mimeType: 'application/pdf',
        createdAt: new Date()
      };
      
      logger.info(`PDF compression completed. ${compressionResult.compressionRatioFormatted} reduction`);
      
      return {
        ...result,
        compressionResult
      };
      
    } catch (error) {
      logger.error('Enhanced PDF compression error:', error);
      throw new Error(`Failed to compress PDF file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get compression preview/estimate without actually compressing
   */
  static async getCompressionEstimate(filePath: string, level: 'low' | 'medium' | 'high'): Promise<{
    estimatedReduction: string;
    settings: CompressionSettings;
    method: 'ghostscript' | 'pdf-lib';
  }> {
    const settings = this.getCompressionSettings(level);
    const useGhostscript = await this.isGhostscriptAvailable();
    
    // Rough estimates based on compression level
    const estimatedReductions = {
      low: '10-20%',
      medium: '30-50%',
      high: '50-70%'
    };
    
    return {
      estimatedReduction: estimatedReductions[level],
      settings,
      method: useGhostscript ? 'ghostscript' : 'pdf-lib'
    };
  }
}
