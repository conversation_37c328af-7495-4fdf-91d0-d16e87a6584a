import { PdfService } from '../../services/PdfService';
import { ConversionService } from '../../services/ConversionService';
import { TestHelpers } from '../utils/testHelpers';

describe('Performance Benchmarks', () => {
  beforeEach(() => {
    TestHelpers.cleanupTestFiles();
  });

  afterAll(() => {
    TestHelpers.cleanupTestFiles();
  });

  describe('PDF Merge Performance', () => {
    it('should merge 10 small PDFs within reasonable time', async () => {
      const startTime = Date.now();
      
      // Create 10 test PDFs with 1 page each
      const pdfPaths = await TestHelpers.createMultipleTestPdfs(10, 1);
      const files = pdfPaths.map(path => 
        TestHelpers.createMockFile(path, `test-${Math.random()}.pdf`)
      );

      const result = await PdfService.mergePdfs({ files });
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result).toBeDefined();
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
      
      // Verify merged PDF has correct number of pages
      const pageCount = await TestHelpers.getPdfPageCount(result.path);
      expect(pageCount).toBe(10);

      console.log(`Merged 10 PDFs in ${duration}ms`);
    });

    it('should merge large PDFs efficiently', async () => {
      const startTime = Date.now();
      
      // Create 3 test PDFs with 10 pages each
      const pdfPaths = await TestHelpers.createMultipleTestPdfs(3, 10);
      const files = pdfPaths.map(path => 
        TestHelpers.createMockFile(path, `large-test-${Math.random()}.pdf`)
      );

      const result = await PdfService.mergePdfs({ files });
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result).toBeDefined();
      expect(duration).toBeLessThan(10000); // Should complete within 10 seconds
      
      // Verify merged PDF has correct number of pages
      const pageCount = await TestHelpers.getPdfPageCount(result.path);
      expect(pageCount).toBe(30);

      console.log(`Merged 3 large PDFs (30 pages total) in ${duration}ms`);
    });
  });

  describe('PDF Split Performance', () => {
    it('should split large PDF efficiently', async () => {
      const startTime = Date.now();
      
      // Create a PDF with 20 pages
      const pdfPath = await TestHelpers.createTestPdf(20);
      const file = TestHelpers.createMockFile(pdfPath, 'large-test.pdf');

      const results = await PdfService.splitPdf({
        file,
        splitType: 'all'
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(results).toHaveLength(20);
      expect(duration).toBeLessThan(15000); // Should complete within 15 seconds

      console.log(`Split 20-page PDF in ${duration}ms`);
    });

    it('should split by specific pages quickly', async () => {
      const startTime = Date.now();
      
      // Create a PDF with 50 pages
      const pdfPath = await TestHelpers.createTestPdf(50);
      const file = TestHelpers.createMockFile(pdfPath, 'huge-test.pdf');

      const results = await PdfService.splitPdf({
        file,
        splitType: 'specific',
        specificPages: '1, 10, 20, 30, 40, 50'
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(results).toHaveLength(6);
      expect(duration).toBeLessThan(8000); // Should complete within 8 seconds

      console.log(`Split specific pages from 50-page PDF in ${duration}ms`);
    });
  });

  describe('PDF Compression Performance', () => {
    it('should compress PDF within reasonable time', async () => {
      const startTime = Date.now();
      
      // Create a PDF with 10 pages
      const pdfPath = await TestHelpers.createTestPdf(10);
      const file = TestHelpers.createMockFile(pdfPath, 'test.pdf');

      const result = await PdfService.compressPdf({
        file,
        compressionLevel: 'medium'
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result).toBeDefined();
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds

      console.log(`Compressed 10-page PDF in ${duration}ms`);
    });
  });

  describe('Conversion Performance', () => {
    it('should convert PDF to images efficiently', async () => {
      const startTime = Date.now();
      
      // Create a PDF with 5 pages
      const pdfPath = await TestHelpers.createTestPdf(5);
      const file = TestHelpers.createMockFile(pdfPath, 'test.pdf');

      const results = await ConversionService.pdfToImage({
        file,
        outputFormat: 'jpg',
        quality: 'medium'
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(results).toHaveLength(5);
      expect(duration).toBeLessThan(8000); // Should complete within 8 seconds

      console.log(`Converted 5-page PDF to images in ${duration}ms`);
    });

    it('should convert Word to PDF quickly', async () => {
      const startTime = Date.now();
      
      const docPath = await TestHelpers.createTestWordDoc();
      const file = TestHelpers.createMockFile(docPath, 'test.html', 'text/html');

      const result = await ConversionService.wordToPdf(file);
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result).toBeDefined();
      expect(duration).toBeLessThan(3000); // Should complete within 3 seconds

      console.log(`Converted Word document to PDF in ${duration}ms`);
    });

    it('should convert multiple images to PDF efficiently', async () => {
      const startTime = Date.now();
      
      // Create 5 test images
      const imagePaths = await Promise.all(
        Array.from({ length: 5 }, () => TestHelpers.createTestImage())
      );
      const files = imagePaths.map(path => 
        TestHelpers.createMockFile(path, `image-${Math.random()}.png`, 'image/png')
      );

      const result = await ConversionService.imagesToPdf(files);
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result).toBeDefined();
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds

      console.log(`Converted 5 images to PDF in ${duration}ms`);
    });
  });

  describe('Memory Usage', () => {
    it('should handle large files without excessive memory usage', async () => {
      const initialMemory = process.memoryUsage();
      
      // Create a large PDF (50 pages)
      const pdfPath = await TestHelpers.createTestPdf(50);
      const file = TestHelpers.createMockFile(pdfPath, 'large-test.pdf');

      // Perform memory-intensive operation
      const result = await PdfService.compressPdf({
        file,
        compressionLevel: 'high'
      });
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      expect(result).toBeDefined();
      // Memory increase should be reasonable (less than 100MB)
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024);

      console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle multiple concurrent operations', async () => {
      const startTime = Date.now();
      
      // Create multiple PDFs for concurrent processing
      const pdfPaths = await TestHelpers.createMultipleTestPdfs(5, 3);
      
      // Run multiple operations concurrently
      const operations = pdfPaths.map(async (pdfPath, index) => {
        const file = TestHelpers.createMockFile(pdfPath, `concurrent-test-${index}.pdf`);
        
        if (index % 2 === 0) {
          return PdfService.compressPdf({ file, compressionLevel: 'medium' });
        } else {
          return PdfService.splitPdf({ file, splitType: 'all' });
        }
      });

      const results = await Promise.all(operations);
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(results).toHaveLength(5);
      expect(duration).toBeLessThan(15000); // Should complete within 15 seconds

      console.log(`Completed 5 concurrent operations in ${duration}ms`);
    });
  });
});
