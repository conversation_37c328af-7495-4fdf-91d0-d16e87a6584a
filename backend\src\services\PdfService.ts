import { PDFDocument, degrees } from 'pdf-lib';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import config from '../config';
import logger from '../utils/logger';
import { FileUtils } from '../utils/fileUtils';
import { CompressionService } from './CompressionService';
import { MemoryManager } from './MemoryManager';
import { <PERSON>rror<PERSON>andler, ApplicationError, ErrorCode } from './ErrorHandler';
import {
  PdfMergeOptions,
  PdfSplitOptions,
  PdfCompressOptions,
  ProcessedFile,
  CompressionResult
} from '../types';

export class PdfService {
  
  /**
   * Merge multiple PDF files into one with memory management
   */
  static async mergePdfs(options: PdfMergeOptions): Promise<ProcessedFile> {
    try {
      logger.info(`Starting PDF merge operation with ${options.files.length} files`);

      // Check initial memory status
      const initialMemory = MemoryManager.checkMemoryLimits();
      logger.info(`Initial memory status: ${initialMemory.message}`);

      // Calculate total file size
      const totalSize = options.files.reduce((sum, file) => sum + file.size, 0);
      const shouldUseStreaming = MemoryManager.shouldUseStreaming(totalSize);

      if (shouldUseStreaming) {
        logger.info(`Large files detected (${FileUtils.formatFileSize(totalSize)}), using memory-optimized processing`);
      }

      const mergedPdf = await PDFDocument.create();

      for (let i = 0; i < options.files.length; i++) {
        const file = options.files[i];
        logger.info(`Processing file ${i + 1}/${options.files.length}: ${file.originalname}`);

        // Check memory before processing each file
        const memoryStatus = MemoryManager.checkMemoryLimits();
        if (memoryStatus.status === 'critical') {
          logger.warn('Critical memory usage detected, forcing garbage collection');
          MemoryManager.forceGarbageCollection();

          // Check again after GC
          const afterGC = MemoryManager.checkMemoryLimits();
          if (afterGC.status === 'critical') {
            throw new Error('Insufficient memory to continue PDF merge operation');
          }
        }

        try {
          // Validate PDF file before processing
          await ErrorHandler.validatePdfFile(file.path);

          const pdfBytes = fs.readFileSync(file.path);
          const pdf = await PDFDocument.load(pdfBytes);
          const pages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());

          pages.forEach((page) => mergedPdf.addPage(page));

          // Clean up temporary file immediately to free memory
          await FileUtils.deleteFile(file.path);

          // Force garbage collection for large files
          if (file.size > 10 * 1024 * 1024) { // 10MB
            MemoryManager.forceGarbageCollection();
          }

        } catch (fileError) {
          logger.error(`Error processing file ${file.originalname}:`, fileError);

          // Convert to ApplicationError if not already
          const appError = ErrorHandler.createError(
            fileError,
            'pdf-merge',
            {
              name: file.originalname,
              size: file.size,
              type: file.mimetype,
              path: file.path
            }
          );

          throw appError;
        }
      }

      // Generate output filename
      const outputName = options.outputName || `merged_${Date.now()}.pdf`;
      const outputPath = path.join(config.paths.uploads, outputName);

      // Save merged PDF with memory monitoring
      logger.info('Saving merged PDF...');
      const pdfBytes = await mergedPdf.save();
      fs.writeFileSync(outputPath, pdfBytes);

      // Final memory check
      const finalMemory = MemoryManager.checkMemoryLimits();
      logger.info(`Final memory status: ${finalMemory.message}`);

      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: pdfBytes.length,
        mimeType: 'application/pdf',
        createdAt: new Date()
      };

      logger.info(`PDF merge completed successfully: ${outputName} (${FileUtils.formatFileSize(pdfBytes.length)})`);
      return result;

    } catch (error) {
      logger.error('PDF merge error:', error);

      // Clean up any remaining temporary files
      for (const file of options.files) {
        await FileUtils.deleteFile(file.path).catch(() => {}); // Ignore errors
      }

      throw new Error(`Failed to merge PDF files: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * Split PDF file based on options
   */
  static async splitPdf(options: PdfSplitOptions): Promise<ProcessedFile[]> {
    try {
      logger.info(`Starting PDF split operation: ${options.splitType}`);
      
      const pdfBytes = fs.readFileSync(options.file.path);
      const pdf = await PDFDocument.load(pdfBytes);
      const totalPages = pdf.getPageCount();
      
      const results: ProcessedFile[] = [];
      
      if (options.splitType === 'all') {
        // Split each page into separate file
        for (let i = 0; i < totalPages; i++) {
          const newPdf = await PDFDocument.create();
          const [page] = await newPdf.copyPages(pdf, [i]);
          newPdf.addPage(page);
          
          const outputName = `page_${i + 1}_${Date.now()}.pdf`;
          const outputPath = path.join(config.paths.uploads, outputName);
          
          const pdfBytes = await newPdf.save();
          fs.writeFileSync(outputPath, pdfBytes);
          
          results.push({
            id: uuidv4(),
            originalName: outputName,
            filename: outputName,
            path: outputPath,
            size: pdfBytes.length,
            mimeType: 'application/pdf',
            createdAt: new Date()
          });
        }
      } else if (options.splitType === 'range' && options.pageRange) {
        // Split by page ranges
        const ranges = this.parsePageRanges(options.pageRange, totalPages);
        
        for (let i = 0; i < ranges.length; i++) {
          const range = ranges[i];
          const newPdf = await PDFDocument.create();
          const pages = await newPdf.copyPages(pdf, range);
          pages.forEach(page => newPdf.addPage(page));
          
          const outputName = `range_${range[0] + 1}-${range[range.length - 1] + 1}_${Date.now()}.pdf`;
          const outputPath = path.join(config.paths.uploads, outputName);
          
          const pdfBytes = await newPdf.save();
          fs.writeFileSync(outputPath, pdfBytes);
          
          results.push({
            id: uuidv4(),
            originalName: outputName,
            filename: outputName,
            path: outputPath,
            size: pdfBytes.length,
            mimeType: 'application/pdf',
            createdAt: new Date()
          });
        }
      } else if (options.splitType === 'specific' && options.specificPages) {
        // Split specific pages
        const pageNumbers = this.parseSpecificPages(options.specificPages, totalPages);
        
        for (const pageNum of pageNumbers) {
          const newPdf = await PDFDocument.create();
          const [page] = await newPdf.copyPages(pdf, [pageNum - 1]);
          newPdf.addPage(page);
          
          const outputName = `page_${pageNum}_${Date.now()}.pdf`;
          const outputPath = path.join(config.paths.uploads, outputName);
          
          const pdfBytes = await newPdf.save();
          fs.writeFileSync(outputPath, pdfBytes);
          
          results.push({
            id: uuidv4(),
            originalName: outputName,
            filename: outputName,
            path: outputPath,
            size: pdfBytes.length,
            mimeType: 'application/pdf',
            createdAt: new Date()
          });
        }
      }
      
      // Clean up original file
      await FileUtils.deleteFile(options.file.path);
      
      logger.info(`PDF split completed successfully: ${results.length} files created`);
      return results;
      
    } catch (error) {
      logger.error('PDF split error:', error);
      throw new Error('Failed to split PDF file');
    }
  }
  
  /**
   * Compress PDF file using enhanced compression service
   */
  static async compressPdf(options: PdfCompressOptions): Promise<ProcessedFile & { compressionResult: CompressionResult }> {
    return CompressionService.compressPdf(options);
  }
  
  /**
   * Rotate PDF pages
   */
  static async rotatePdf(file: Express.Multer.File, rotation: number, pages?: string): Promise<ProcessedFile> {
    try {
      logger.info(`Starting PDF rotation: ${rotation} degrees`);

      const pdfBytes = fs.readFileSync(file.path);
      const pdf = await PDFDocument.load(pdfBytes);
      const totalPages = pdf.getPageCount();

      // Determine which pages to rotate
      let pageIndices: number[];
      if (!pages || pages === 'all') {
        pageIndices = Array.from({ length: totalPages }, (_, i) => i);
      } else {
        pageIndices = this.parseSpecificPages(pages, totalPages).map(p => p - 1);
      }

      // Rotate specified pages
      for (const pageIndex of pageIndices) {
        const page = pdf.getPage(pageIndex);
        page.setRotation(degrees(rotation));
      }

      const outputName = `rotated_${file.originalname}`;
      const outputPath = path.join(config.paths.uploads, outputName);

      const rotatedBytes = await pdf.save();
      fs.writeFileSync(outputPath, rotatedBytes);

      // Clean up original file
      await FileUtils.deleteFile(file.path);

      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: rotatedBytes.length,
        mimeType: 'application/pdf',
        createdAt: new Date()
      };

      logger.info(`PDF rotation completed successfully`);
      return result;

    } catch (error) {
      logger.error('PDF rotation error:', error);
      throw new Error('Failed to rotate PDF file');
    }
  }

  /**
   * Parse page ranges like "1-5, 8-12"
   */
  private static parsePageRanges(rangeStr: string, totalPages: number): number[][] {
    const ranges: number[][] = [];
    const parts = rangeStr.split(',').map(s => s.trim());

    for (const part of parts) {
      if (part.includes('-')) {
        const [start, end] = part.split('-').map(s => parseInt(s.trim()));
        const range: number[] = [];
        for (let i = Math.max(1, start); i <= Math.min(totalPages, end); i++) {
          range.push(i - 1); // Convert to 0-based index
        }
        if (range.length > 0) ranges.push(range);
      } else {
        const pageNum = parseInt(part);
        if (pageNum >= 1 && pageNum <= totalPages) {
          ranges.push([pageNum - 1]); // Convert to 0-based index
        }
      }
    }

    return ranges;
  }

  /**
   * Parse specific pages like "1, 3, 5, 7"
   */
  private static parseSpecificPages(pagesStr: string, totalPages: number): number[] {
    const pages: number[] = [];
    const parts = pagesStr.split(',').map(s => s.trim());

    for (const part of parts) {
      const pageNum = parseInt(part);
      if (pageNum >= 1 && pageNum <= totalPages) {
        pages.push(pageNum);
      }
    }

    return pages;
  }
}
