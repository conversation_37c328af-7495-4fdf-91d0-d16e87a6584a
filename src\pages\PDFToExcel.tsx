import React, { useState } from 'react';
import { FileText, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import apiService from '../services/api';

const PDFToExcel = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [outputFormat, setOutputFormat] = useState<'xlsx' | 'xls'>('xlsx');
  const [downloadUrl, setDownloadUrl] = useState<string>('');
  const [error, setError] = useState<string>('');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    setDownloadUrl('');
    setError('');
  };

  const handleConvert = async () => {
    if (files.length === 0) return;

    setIsProcessing(true);
    setError('');

    try {
      const response = await apiService.pdfToExcel(files[0]);

      if (response.success && response.data) {
        setDownloadUrl(response.data.downloadUrl || '');
        alert(response.message || 'PDF converti en Excel avec succès!');
      } else {
        throw new Error(response.error || 'Erreur lors de la conversion');
      }
    } catch (error) {
      console.error('Conversion error:', error);
      setError(error instanceof Error ? error.message : 'Erreur lors de la conversion PDF vers Excel');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownload = async () => {
    if (!downloadUrl) return;

    try {
      const fileId = downloadUrl.split('/').pop();
      if (!fileId) return;

      const blob = await apiService.downloadFile(fileId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `converted.${outputFormat}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download error:', error);
      setError('Erreur lors du téléchargement');
    }
  };

  return (
    <ToolLayout
      title="PDF en Excel"
      description="Transférez les données de fichiers PDF vers des feuilles de calcul Excel en quelques secondes"
      icon={<FileText className="w-8 h-8" />}
      color="from-emerald-500 to-emerald-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Format de sortie
            </h3>
            
            <div className="space-y-4">
              <label className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="format"
                  value="xlsx"
                  checked={outputFormat === 'xlsx'}
                  onChange={(e) => setOutputFormat(e.target.value as 'xlsx')}
                  className="text-emerald-600"
                />
                <div>
                  <span className="text-slate-700 font-medium">XLSX (recommandé)</span>
                  <p className="text-sm text-slate-500">Compatible avec Excel 2007 et versions ultérieures</p>
                </div>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="format"
                  value="xls"
                  checked={outputFormat === 'xls'}
                  onChange={(e) => setOutputFormat(e.target.value as 'xls')}
                  className="text-emerald-600"
                />
                <div>
                  <span className="text-slate-700 font-medium">XLS</span>
                  <p className="text-sm text-slate-500">Compatible avec les anciennes versions d'Excel</p>
                </div>
              </label>
            </div>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-emerald-600 to-green-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <span>Convertir en Excel</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}

        {downloadUrl && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-green-800">PDF converti en Excel avec succès!</h3>
                <p className="text-green-600">Votre fichier Excel est prêt à être téléchargé.</p>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-5 h-5" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default PDFToExcel;